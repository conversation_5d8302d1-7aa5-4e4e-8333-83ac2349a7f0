/*
 * MSPM0G3507 SPI主机控制DAC904 FPGA - 修复版本
 * 硬件连接：
 * PA6 (SPI_SCLK) → FPGA PIN_23 (spi_sclk)
 * PA5 (SPI_MOSI) → FPGA PIN_25 (spi_mosi)  
 * PA4 (GPIO_CS)  → FPGA PIN_31 (spi_cs_n)
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>

// 手动定义GPIO宏（如果SysConfig没有生成）
#define CS_PORT     GPIOA
#define CS_PIN      DL_GPIO_PIN_4

// 频率档位定义
typedef enum {
    FREQ_1KHZ = 0,      // 1KHz
    FREQ_10KHZ = 1,     // 10KHz  
    FREQ_100KHZ = 2,    // 100KHz
    FREQ_1MHZ = 3,      // 1MHz
    FREQ_1_5MHZ = 4     // 1.5MHz
} freq_level_t;

// 函数声明
void delay_ms(uint32_t ms);
void set_dac_params(freq_level_t freq_level, uint8_t amplitude_percent);
void gpio_init(void);

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 手动初始化GPIO（备用方案）
    gpio_init();
    
    // 延时等待系统稳定
    delay_ms(100);
    
    // 主循环演示
    while(1) {
        // 测试序列1：频率扫描，固定75%幅度
        set_dac_params(FREQ_1KHZ, 75);
        delay_ms(2000);
        
        set_dac_params(FREQ_10KHZ, 75);
        delay_ms(2000);
        
        set_dac_params(FREQ_100KHZ, 75);
        delay_ms(2000);
        
        set_dac_params(FREQ_1MHZ, 75);
        delay_ms(2000);
        
        // 测试序列2：幅度扫描，固定10KHz
        for(int amp = 25; amp <= 100; amp += 25) {
            set_dac_params(FREQ_10KHZ, amp);
            delay_ms(1000);
        }
    }
}

/**
 * 手动GPIO初始化（备用方案）
 */
void gpio_init(void)
{
    // 使能GPIOA时钟
    DL_GPIO_enablePower(CS_PORT);
    
    // 配置PA4为输出，初始为高电平
    DL_GPIO_initDigitalOutput(IOMUX_PINCM4);
    DL_GPIO_setPins(CS_PORT, CS_PIN);
    DL_GPIO_enableOutput(CS_PORT, CS_PIN);
}

/**
 * 设置DAC参数
 * @param freq_level: 频率档位 (0-4)
 * @param amplitude_percent: 幅度百分比 (0-100)
 */
void set_dac_params(freq_level_t freq_level, uint8_t amplitude_percent)
{
    // 参数检查
    if(freq_level > FREQ_1_5MHZ) freq_level = FREQ_1KHZ;
    if(amplitude_percent > 100) amplitude_percent = 100;
    
    // 计算幅度值 (0-100% 映射到 0-4095)
    uint16_t amplitude = (amplitude_percent * 4095) / 100;
    
    // 组合16位数据：[15:12]频率档位 + [11:0]幅度
    uint16_t spi_data = ((uint16_t)freq_level << 12) | (amplitude & 0x0FFF);
    
    // SPI传输
    DL_GPIO_clearPins(CS_PORT, CS_PIN);  // CS拉低
    
    // 发送16位数据
    DL_SPI_transmitData16(SPI_0_INST, spi_data);
    
    // 等待传输完成
    while(DL_SPI_isBusy(SPI_0_INST));
    
    DL_GPIO_setPins(CS_PORT, CS_PIN);    // CS拉高
    
    // 短暂延时确保FPGA处理完成
    delay_ms(1);
}

/**
 * 毫秒延时函数
 */
void delay_ms(uint32_t ms)
{
    // 假设系统时钟32MHz
    uint32_t cycles = ms * 32000;
    DL_Common_delayCycles(cycles);
}

/**
 * 简化版本 - 用于快速测试
 */
void simple_test(void)
{
    while(1) {
        // 发送：档位1(10KHz) + 75%幅度
        uint16_t data = 0x1C00;  // 0001 1100 0000 0000
        
        DL_GPIO_clearPins(CS_PORT, CS_PIN);
        DL_SPI_transmitData16(SPI_0_INST, data);
        while(DL_SPI_isBusy(SPI_0_INST));
        DL_GPIO_setPins(CS_PORT, CS_PIN);
        
        delay_ms(1000);
        
        // 发送：档位2(100KHz) + 50%幅度  
        data = 0x2800;  // 0010 1000 0000 0000
        
        DL_GPIO_clearPins(CS_PORT, CS_PIN);
        DL_SPI_transmitData16(SPI_0_INST, data);
        while(DL_SPI_isBusy(SPI_0_INST));
        DL_GPIO_setPins(CS_PORT, CS_PIN);
        
        delay_ms(1000);
    }
}
