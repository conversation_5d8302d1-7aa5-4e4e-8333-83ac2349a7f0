Analysis & Synthesis report for DAC904
Fri Aug 01 10:14:08 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+---------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                    ;
+------------------------------------+--------------------------------------------+
; Analysis & Synthesis Status        ; Failed - Fri Aug 01 10:14:08 2025          ;
; Quartus II 64-Bit Version          ; 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name                      ; DAC904                                     ;
; Top-level Entity Name              ; DAC904_TOP                                 ;
; Family                             ; Cyclone IV E                               ;
; Total logic elements               ; N/A until Partition Merge                  ;
;     Total combinational functions  ; N/A until Partition Merge                  ;
;     Dedicated logic registers      ; N/A until Partition Merge                  ;
; Total registers                    ; N/A until Partition Merge                  ;
; Total pins                         ; N/A until Partition Merge                  ;
; Total virtual pins                 ; N/A until Partition Merge                  ;
; Total memory bits                  ; N/A until Partition Merge                  ;
; Embedded Multiplier 9-bit elements ; N/A until Partition Merge                  ;
; Total PLLs                         ; N/A until Partition Merge                  ;
+------------------------------------+--------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                        ;
+----------------------------------------------------------------------------+--------------------+--------------------+
; Option                                                                     ; Setting            ; Default Value      ;
+----------------------------------------------------------------------------+--------------------+--------------------+
; Device                                                                     ; EP4CE6E22C8        ;                    ;
; Top-level entity name                                                      ; DAC904_TOP         ; DAC904             ;
; Family name                                                                ; Cyclone IV E       ; Cyclone IV GX      ;
; Use smart compilation                                                      ; Off                ; Off                ;
; Enable parallel Assembler and TimeQuest Timing Analyzer during compilation ; On                 ; On                 ;
; Enable compact report table                                                ; Off                ; Off                ;
; Restructure Multiplexers                                                   ; Auto               ; Auto               ;
; Create Debugging Nodes for IP Cores                                        ; Off                ; Off                ;
; Preserve fewer node names                                                  ; On                 ; On                 ;
; Disable OpenCore Plus hardware evaluation                                  ; Off                ; Off                ;
; Verilog Version                                                            ; Verilog_2001       ; Verilog_2001       ;
; VHDL Version                                                               ; VHDL_1993          ; VHDL_1993          ;
; State Machine Processing                                                   ; Auto               ; Auto               ;
; Safe State Machine                                                         ; Off                ; Off                ;
; Extract Verilog State Machines                                             ; On                 ; On                 ;
; Extract VHDL State Machines                                                ; On                 ; On                 ;
; Ignore Verilog initial constructs                                          ; Off                ; Off                ;
; Iteration limit for constant Verilog loops                                 ; 5000               ; 5000               ;
; Iteration limit for non-constant Verilog loops                             ; 250                ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                                    ; On                 ; On                 ;
; Infer RAMs from Raw Logic                                                  ; On                 ; On                 ;
; Parallel Synthesis                                                         ; On                 ; On                 ;
; DSP Block Balancing                                                        ; Auto               ; Auto               ;
; NOT Gate Push-Back                                                         ; On                 ; On                 ;
; Power-Up Don't Care                                                        ; On                 ; On                 ;
; Remove Redundant Logic Cells                                               ; Off                ; Off                ;
; Remove Duplicate Registers                                                 ; On                 ; On                 ;
; Ignore CARRY Buffers                                                       ; Off                ; Off                ;
; Ignore CASCADE Buffers                                                     ; Off                ; Off                ;
; Ignore GLOBAL Buffers                                                      ; Off                ; Off                ;
; Ignore ROW GLOBAL Buffers                                                  ; Off                ; Off                ;
; Ignore LCELL Buffers                                                       ; Off                ; Off                ;
; Ignore SOFT Buffers                                                        ; On                 ; On                 ;
; Limit AHDL Integers to 32 Bits                                             ; Off                ; Off                ;
; Optimization Technique                                                     ; Balanced           ; Balanced           ;
; Carry Chain Length                                                         ; 70                 ; 70                 ;
; Auto Carry Chains                                                          ; On                 ; On                 ;
; Auto Open-Drain Pins                                                       ; On                 ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                                      ; Off                ; Off                ;
; Auto ROM Replacement                                                       ; On                 ; On                 ;
; Auto RAM Replacement                                                       ; On                 ; On                 ;
; Auto DSP Block Replacement                                                 ; On                 ; On                 ;
; Auto Shift Register Replacement                                            ; Auto               ; Auto               ;
; Allow Shift Register Merging across Hierarchies                            ; Auto               ; Auto               ;
; Auto Clock Enable Replacement                                              ; On                 ; On                 ;
; Strict RAM Replacement                                                     ; Off                ; Off                ;
; Allow Synchronous Control Signals                                          ; On                 ; On                 ;
; Force Use of Synchronous Clear Signals                                     ; Off                ; Off                ;
; Auto RAM Block Balancing                                                   ; On                 ; On                 ;
; Auto RAM to Logic Cell Conversion                                          ; Off                ; Off                ;
; Auto Resource Sharing                                                      ; Off                ; Off                ;
; Allow Any RAM Size For Recognition                                         ; Off                ; Off                ;
; Allow Any ROM Size For Recognition                                         ; Off                ; Off                ;
; Allow Any Shift Register Size For Recognition                              ; Off                ; Off                ;
; Use LogicLock Constraints during Resource Balancing                        ; On                 ; On                 ;
; Ignore translate_off and synthesis_off directives                          ; Off                ; Off                ;
; Timing-Driven Synthesis                                                    ; On                 ; On                 ;
; Report Parameter Settings                                                  ; On                 ; On                 ;
; Report Source Assignments                                                  ; On                 ; On                 ;
; Report Connectivity Checks                                                 ; On                 ; On                 ;
; Ignore Maximum Fan-Out Assignments                                         ; Off                ; Off                ;
; Synchronization Register Chain Length                                      ; 2                  ; 2                  ;
; PowerPlay Power Optimization                                               ; Normal compilation ; Normal compilation ;
; HDL message level                                                          ; Level2             ; Level2             ;
; Suppress Register Optimization Related Messages                            ; Off                ; Off                ;
; Number of Removed Registers Reported in Synthesis Report                   ; 5000               ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report                         ; 5000               ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report                  ; 100                ; 100                ;
; Clock MUX Protection                                                       ; On                 ; On                 ;
; Auto Gated Clock Conversion                                                ; Off                ; Off                ;
; Block Design Naming                                                        ; Auto               ; Auto               ;
; SDC constraint protection                                                  ; Off                ; Off                ;
; Synthesis Effort                                                           ; Auto               ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal               ; On                 ; On                 ;
; Pre-Mapping Resynthesis Optimization                                       ; Off                ; Off                ;
; Analysis & Synthesis Message Level                                         ; Medium             ; Medium             ;
; Disable Register Merging Across Hierarchies                                ; Auto               ; Auto               ;
; Resource Aware Inference For Block RAM                                     ; On                 ; On                 ;
; Synthesis Seed                                                             ; 1                  ; 1                  ;
+----------------------------------------------------------------------------+--------------------+--------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit Analysis & Synthesis
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Fri Aug 01 10:14:08 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v
    Info (12023): Found entity 1: ROM_Tri
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v
    Info (12023): Found entity 1: sel_wave
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v
    Info (12023): Found entity 1: key_delay
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v
    Info (12023): Found entity 1: key_con
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v
    Info (12023): Found entity 1: add_32bit
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v
    Info (12023): Found entity 1: ROM_Sin
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v
    Info (12023): Found entity 1: DAC904_TOP
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v
    Info (12023): Found entity 1: PLL_CLK
Error (10161): Verilog HDL error at DAC904_TOP.v(67): object "spi_data_temp" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 67
Error (10161): Verilog HDL error at DAC904_TOP.v(70): object "data_valid" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 70
Error (10161): Verilog HDL error at DAC904_TOP.v(80): object "data_valid" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 80
Error (10161): Verilog HDL error at DAC904_TOP.v(84): object "spi_data_temp" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 84
Error (10161): Verilog HDL error at DAC904_TOP.v(88): object "spi_data_temp" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 88
Error (10161): Verilog HDL error at DAC904_TOP.v(89): object "data_valid" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 89
Error (10161): Verilog HDL error at DAC904_TOP.v(94): object "data_valid" is not declared File: C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v Line: 94
Error: Quartus II 64-Bit Analysis & Synthesis was unsuccessful. 7 errors, 1 warning
    Error: Peak virtual memory: 4608 megabytes
    Error: Processing ended: Fri Aug 01 10:14:08 2025
    Error: Elapsed time: 00:00:00
    Error: Total CPU time (on all processors): 00:00:00


