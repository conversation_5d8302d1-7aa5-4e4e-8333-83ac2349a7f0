// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 10:17:21")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1158:1158:1158) (1153:1153:1153))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1696:1696:1696) (1384:1384:1384))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1853:1853:1853) (1480:1480:1480))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1881:1881:1881) (1468:1468:1468))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1407:1407:1407) (1156:1156:1156))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1929:1929:1929) (1529:1529:1529))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1850:1850:1850) (1441:1441:1441))
        (IOPATH i o (3985:3985:3985) (4026:4026:4026))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1325:1325:1325) (1054:1054:1054))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1329:1329:1329) (1059:1059:1059))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1234:1234:1234) (979:979:979))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1262:1262:1262) (1016:1016:1016))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1683:1683:1683) (1354:1354:1354))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2082:2082:2082) (1708:1708:1708))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1788:1788:1788) (1525:1525:1525))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1666:1666:1666) (2019:2019:2019))
        (IOPATH i o (2696:2696:2696) (2717:2717:2717))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1167:1167:1167) (1167:1167:1167))
        (PORT inclk[0] (2039:2039:2039) (2039:2039:2039))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2044:2044:2044) (2010:2010:2010))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1350:1350:1350) (1592:1592:1592))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_d)
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT asdata (1792:1792:1792) (1998:1998:1998))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1407:1407:1407) (1620:1620:1620))
        (PORT datad (3345:3345:3345) (3438:3438:3438))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (499:499:499) (490:490:490))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT asdata (921:921:921) (919:919:919))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT asdata (723:723:723) (785:785:785))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT asdata (749:749:749) (811:811:811))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (302:302:302) (359:359:359))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (302:302:302) (358:358:358))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (495:495:495) (489:489:489))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (301:301:301) (357:357:357))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (325:325:325) (380:380:380))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (365:365:365) (421:421:421))
        (IOPATH datab combout (438:438:438) (455:455:455))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1439:1439:1439) (1484:1484:1484))
        (PORT asdata (724:724:724) (786:786:786))
        (PORT clrn (1491:1491:1491) (1419:1419:1419))
        (PORT ena (1196:1196:1196) (1093:1093:1093))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1492:1492:1492))
        (PORT asdata (2053:2053:2053) (1880:1880:1880))
        (PORT clrn (1515:1515:1515) (1444:1444:1444))
        (PORT ena (2360:2360:2360) (2116:2116:2116))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[13\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (422:422:422) (523:523:523))
        (IOPATH datac combout (301:301:301) (283:283:283))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1492:1492:1492))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1515:1515:1515) (1444:1444:1444))
        (PORT ena (2360:2360:2360) (2116:2116:2116))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1492:1492:1492))
        (PORT asdata (959:959:959) (946:946:946))
        (PORT clrn (1515:1515:1515) (1444:1444:1444))
        (PORT ena (2360:2360:2360) (2116:2116:2116))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1492:1492:1492))
        (PORT asdata (948:948:948) (936:936:936))
        (PORT clrn (1515:1515:1515) (1444:1444:1444))
        (PORT ena (2360:2360:2360) (2116:2116:2116))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (463:463:463) (573:573:573))
        (PORT datab (457:457:457) (552:552:552))
        (PORT datac (416:416:416) (516:516:516))
        (PORT datad (620:620:620) (627:627:627))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1068:1068:1068) (985:985:985))
        (PORT datab (1042:1042:1042) (961:961:961))
        (PORT datac (588:588:588) (579:579:579))
        (PORT datad (946:946:946) (885:885:885))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1063:1063:1063) (980:980:980))
        (PORT datab (1040:1040:1040) (959:959:959))
        (PORT datac (594:594:594) (587:587:587))
        (PORT datad (941:941:941) (880:880:880))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (462:462:462) (572:572:572))
        (PORT datab (461:461:461) (555:555:555))
        (PORT datac (420:420:420) (521:521:521))
        (PORT datad (623:623:623) (630:630:630))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (465:465:465) (575:575:575))
        (PORT datab (454:454:454) (548:548:548))
        (PORT datac (412:412:412) (512:512:512))
        (PORT datad (617:617:617) (624:624:624))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (460:460:460) (570:570:570))
        (PORT datab (465:465:465) (560:560:560))
        (PORT datac (426:426:426) (526:526:526))
        (PORT datad (627:627:627) (635:635:635))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (466:466:466) (576:576:576))
        (PORT datab (451:451:451) (545:545:545))
        (PORT datac (408:408:408) (508:508:508))
        (PORT datad (614:614:614) (621:621:621))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (465:465:465) (575:575:575))
        (PORT datab (453:453:453) (547:547:547))
        (PORT datac (411:411:411) (511:511:511))
        (PORT datad (616:616:616) (623:623:623))
        (IOPATH dataa combout (420:420:420) (444:444:444))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (461:461:461) (570:570:570))
        (PORT datab (464:464:464) (559:559:559))
        (PORT datac (424:424:424) (525:525:525))
        (PORT datad (626:626:626) (634:634:634))
        (IOPATH dataa combout (435:435:435) (449:449:449))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (461:461:461) (571:571:571))
        (PORT datab (462:462:462) (557:557:557))
        (PORT datac (422:422:422) (522:522:522))
        (PORT datad (624:624:624) (631:631:631))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (464:464:464) (574:574:574))
        (PORT datab (455:455:455) (549:549:549))
        (PORT datac (413:413:413) (513:513:513))
        (PORT datad (617:617:617) (625:625:625))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (461:461:461) (571:571:571))
        (PORT datab (463:463:463) (558:558:558))
        (PORT datac (423:423:423) (523:523:523))
        (PORT datad (624:624:624) (632:632:632))
        (IOPATH dataa combout (428:428:428) (450:450:450))
        (IOPATH datab combout (423:423:423) (380:380:380))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (462:462:462) (572:572:572))
        (PORT datab (458:458:458) (553:553:553))
        (PORT datac (417:417:417) (518:518:518))
        (PORT datad (621:621:621) (628:628:628))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (465:465:465) (575:575:575))
        (PORT datab (451:451:451) (545:545:545))
        (PORT datac (409:409:409) (509:509:509))
        (PORT datad (614:614:614) (621:621:621))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH datab combout (400:400:400) (391:391:391))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (453:453:453) (552:552:552))
        (PORT datab (573:573:573) (553:553:553))
        (PORT datad (611:611:611) (618:618:618))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (340:340:340) (400:400:400))
        (PORT datab (447:447:447) (540:540:540))
        (PORT datad (424:424:424) (525:525:525))
        (IOPATH dataa combout (373:373:373) (380:380:380))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (454:454:454) (553:553:553))
        (PORT datab (449:449:449) (543:543:543))
        (PORT datad (423:423:423) (524:524:524))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1828:1828:1828) (1548:1548:1548))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1240:1240:1240) (1036:1036:1036))
        (PORT datab (315:315:315) (369:369:369))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1573:1573:1573) (1360:1360:1360))
        (PORT datab (315:315:315) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (957:957:957) (815:815:815))
        (PORT datab (534:534:534) (506:506:506))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1640:1640:1640) (1434:1434:1434))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1566:1566:1566) (1356:1356:1356))
        (PORT datab (534:534:534) (506:506:506))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (836:836:836) (731:731:731))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (958:958:958) (816:816:816))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (533:533:533) (512:512:512))
        (PORT datab (903:903:903) (790:790:790))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (530:530:530) (518:518:518))
        (PORT datab (897:897:897) (764:764:764))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (910:910:910) (776:776:776))
        (PORT datab (317:317:317) (371:371:371))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (853:853:853) (754:754:754))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (534:534:534) (514:514:514))
        (PORT datab (903:903:903) (790:790:790))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (377:377:377))
        (PORT datab (857:857:857) (740:740:740))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (916:916:916) (780:780:780))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (414:414:414) (450:450:450))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (318:318:318) (377:377:377))
        (PORT datab (1191:1191:1191) (1019:1019:1019))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1450:1450:1450) (1491:1491:1491))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1778:1778:1778) (1651:1651:1651))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (596:596:596) (547:547:547))
        (PORT datab (878:878:878) (767:767:767))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (850:850:850) (753:753:753))
        (PORT datab (316:316:316) (370:370:370))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (423:423:423) (453:453:453))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (914:914:914) (783:783:783))
        (PORT datab (316:316:316) (371:371:371))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (378:378:378))
        (PORT datab (950:950:950) (817:817:817))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (542:542:542) (538:538:538))
        (PORT datab (919:919:919) (794:794:794))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (1546:1546:1546) (1291:1291:1291))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (912:912:912) (781:781:781))
        (PORT datab (538:538:538) (533:533:533))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (550:550:550) (537:537:537))
        (PORT datab (950:950:950) (817:817:817))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1070:1070:1070) (986:986:986))
        (PORT datab (1042:1042:1042) (962:962:962))
        (PORT datac (587:587:587) (578:578:578))
        (PORT datad (947:947:947) (887:887:887))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datac combout (305:305:305) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (553:553:553) (546:546:546))
        (PORT datab (820:820:820) (714:714:714))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1285:1285:1285) (1199:1199:1199))
        (PORT datab (955:955:955) (811:811:811))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1065:1065:1065) (982:982:982))
        (PORT datab (1040:1040:1040) (960:960:960))
        (PORT datac (593:593:593) (586:586:586))
        (PORT datad (942:942:942) (881:881:881))
        (IOPATH dataa combout (375:375:375) (371:371:371))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (544:544:544) (527:527:527))
        (PORT datab (881:881:881) (767:767:767))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (365:365:365) (425:425:425))
        (PORT datab (949:949:949) (815:815:815))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (572:572:572) (565:565:565))
        (PORT datab (920:920:920) (795:795:795))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1064:1064:1064) (981:981:981))
        (PORT datab (1040:1040:1040) (960:960:960))
        (PORT datac (594:594:594) (587:587:587))
        (PORT datad (942:942:942) (881:881:881))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (900:900:900) (767:767:767))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1066:1066:1066) (983:983:983))
        (PORT datab (1041:1041:1041) (961:961:961))
        (PORT datac (592:592:592) (585:585:585))
        (PORT datad (943:943:943) (883:883:883))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (377:377:377) (380:380:380))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (940:940:940) (799:799:799))
        (PORT datab (362:362:362) (417:417:417))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (444:444:444))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1462:1462:1462) (1504:1504:1504))
        (PORT d (90:90:90) (101:101:101))
        (PORT sclr (1787:1787:1787) (1661:1661:1661))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD sclr (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1313:1313:1313) (1225:1225:1225))
        (PORT d[1] (1379:1379:1379) (1277:1277:1277))
        (PORT d[2] (1323:1323:1323) (1224:1224:1224))
        (PORT d[3] (1335:1335:1335) (1244:1244:1244))
        (PORT d[4] (1007:1007:1007) (960:960:960))
        (PORT d[5] (952:952:952) (915:915:915))
        (PORT d[6] (2980:2980:2980) (2657:2657:2657))
        (PORT d[7] (1087:1087:1087) (1038:1038:1038))
        (PORT d[8] (1028:1028:1028) (983:983:983))
        (PORT d[9] (1729:1729:1729) (1579:1579:1579))
        (PORT d[10] (1012:1012:1012) (972:972:972))
        (PORT d[11] (1632:1632:1632) (1464:1464:1464))
        (PORT clk (1816:1816:1816) (1879:1879:1879))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1816:1816:1816) (1879:1879:1879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1880:1880:1880))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1771:1771:1771) (1832:1832:1832))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (843:843:843) (866:866:866))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1255:1255:1255) (1080:1080:1080))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1459:1459:1459) (1501:1501:1501))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1524:1524:1524) (1451:1451:1451))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1216:1216:1216) (1061:1061:1061))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1459:1459:1459) (1501:1501:1501))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1524:1524:1524) (1451:1451:1451))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1373:1373:1373) (1256:1256:1256))
        (PORT d[1] (1742:1742:1742) (1585:1585:1585))
        (PORT d[2] (1291:1291:1291) (1202:1202:1202))
        (PORT d[3] (1720:1720:1720) (1578:1578:1578))
        (PORT d[4] (947:947:947) (907:907:907))
        (PORT d[5] (991:991:991) (941:941:941))
        (PORT d[6] (3331:3331:3331) (2973:2973:2973))
        (PORT d[7] (985:985:985) (942:942:942))
        (PORT d[8] (964:964:964) (924:924:924))
        (PORT d[9] (1667:1667:1667) (1523:1523:1523))
        (PORT d[10] (997:997:997) (955:955:955))
        (PORT d[11] (1307:1307:1307) (1183:1183:1183))
        (PORT clk (1817:1817:1817) (1880:1880:1880))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1817:1817:1817) (1880:1880:1880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1881:1881:1881))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1772:1772:1772) (1833:1833:1833))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (844:844:844) (867:867:867))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (868:868:868))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (868:868:868))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (868:868:868))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1251:1251:1251) (1087:1087:1087))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1441:1441:1441) (1485:1485:1485))
        (PORT asdata (1913:1913:1913) (1689:1689:1689))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1668:1668:1668) (1523:1523:1523))
        (PORT d[1] (1771:1771:1771) (1606:1606:1606))
        (PORT d[2] (1740:1740:1740) (1574:1574:1574))
        (PORT d[3] (1403:1403:1403) (1297:1297:1297))
        (PORT d[4] (972:972:972) (935:935:935))
        (PORT d[5] (1001:1001:1001) (955:955:955))
        (PORT d[6] (2886:2886:2886) (2572:2572:2572))
        (PORT d[7] (1065:1065:1065) (1021:1021:1021))
        (PORT d[8] (1036:1036:1036) (992:992:992))
        (PORT d[9] (1389:1389:1389) (1279:1279:1279))
        (PORT d[10] (1063:1063:1063) (1016:1016:1016))
        (PORT d[11] (1327:1327:1327) (1205:1205:1205))
        (PORT clk (1825:1825:1825) (1891:1891:1891))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1825:1825:1825) (1891:1891:1891))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1826:1826:1826) (1892:1892:1892))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1780:1780:1780) (1844:1844:1844))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (852:852:852) (878:878:878))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (853:853:853) (879:879:879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (853:853:853) (879:879:879))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (853:853:853) (879:879:879))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1539:1539:1539) (1311:1311:1311))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1432:1432:1432) (1475:1475:1475))
        (PORT asdata (1953:1953:1953) (1728:1728:1728))
        (PORT clrn (1498:1498:1498) (1424:1424:1424))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1288:1288:1288) (1205:1205:1205))
        (PORT d[1] (1382:1382:1382) (1278:1278:1278))
        (PORT d[2] (1284:1284:1284) (1194:1194:1194))
        (PORT d[3] (1358:1358:1358) (1260:1260:1260))
        (PORT d[4] (971:971:971) (934:934:934))
        (PORT d[5] (1338:1338:1338) (1249:1249:1249))
        (PORT d[6] (2942:2942:2942) (2622:2622:2622))
        (PORT d[7] (1769:1769:1769) (1634:1634:1634))
        (PORT d[8] (992:992:992) (957:957:957))
        (PORT d[9] (1349:1349:1349) (1249:1249:1249))
        (PORT d[10] (1093:1093:1093) (1042:1042:1042))
        (PORT d[11] (1320:1320:1320) (1197:1197:1197))
        (PORT clk (1827:1827:1827) (1892:1892:1892))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1827:1827:1827) (1892:1892:1892))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1828:1828:1828) (1893:1893:1893))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1782:1782:1782) (1845:1845:1845))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (854:854:854) (879:879:879))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (880:880:880))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1271:1271:1271) (1106:1106:1106))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1428:1428:1428) (1470:1470:1470))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1493:1493:1493) (1420:1420:1420))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (951:951:951) (822:822:822))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1436:1436:1436) (1482:1482:1482))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1490:1490:1490) (1416:1416:1416))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1680:1680:1680) (1537:1537:1537))
        (PORT d[1] (1662:1662:1662) (1526:1526:1526))
        (PORT d[2] (1712:1712:1712) (1558:1558:1558))
        (PORT d[3] (1719:1719:1719) (1563:1563:1563))
        (PORT d[4] (1399:1399:1399) (1298:1298:1298))
        (PORT d[5] (1324:1324:1324) (1240:1240:1240))
        (PORT d[6] (2510:2510:2510) (2239:2239:2239))
        (PORT d[7] (1428:1428:1428) (1333:1333:1333))
        (PORT d[8] (1425:1425:1425) (1325:1325:1325))
        (PORT d[9] (1702:1702:1702) (1556:1556:1556))
        (PORT d[10] (1487:1487:1487) (1381:1381:1381))
        (PORT d[11] (1401:1401:1401) (1266:1266:1266))
        (PORT clk (1827:1827:1827) (1893:1893:1893))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1827:1827:1827) (1893:1893:1893))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1828:1828:1828) (1894:1894:1894))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1782:1782:1782) (1846:1846:1846))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (854:854:854) (880:880:880))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (855:855:855) (881:881:881))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1255:1255:1255) (1091:1091:1091))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1448:1448:1448) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1511:1511:1511) (1440:1440:1440))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1288:1288:1288) (1113:1113:1113))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1448:1448:1448) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1511:1511:1511) (1440:1440:1440))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1710:1710:1710) (1568:1568:1568))
        (PORT d[1] (999:999:999) (934:934:934))
        (PORT d[2] (940:940:940) (883:883:883))
        (PORT d[3] (1732:1732:1732) (1591:1591:1591))
        (PORT d[4] (1335:1335:1335) (1236:1236:1236))
        (PORT d[5] (1316:1316:1316) (1229:1229:1229))
        (PORT d[6] (1418:1418:1418) (1304:1304:1304))
        (PORT d[7] (989:989:989) (915:915:915))
        (PORT d[8] (970:970:970) (910:910:910))
        (PORT d[9] (1648:1648:1648) (1510:1510:1510))
        (PORT d[10] (950:950:950) (891:891:891))
        (PORT d[11] (1335:1335:1335) (1200:1200:1200))
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1829:1829:1829) (1894:1894:1894))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1830:1830:1830) (1895:1895:1895))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1784:1784:1784) (1847:1847:1847))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (856:856:856) (881:881:881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (857:857:857) (882:882:882))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1567:1567:1567) (1337:1337:1337))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1486:1486:1486))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1509:1509:1509) (1432:1432:1432))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1601:1601:1601) (1410:1410:1410))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1440:1440:1440) (1486:1486:1486))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1509:1509:1509) (1432:1432:1432))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1350:1350:1350) (1247:1247:1247))
        (PORT d[1] (1377:1377:1377) (1268:1268:1268))
        (PORT d[2] (1342:1342:1342) (1251:1251:1251))
        (PORT d[3] (1313:1313:1313) (1217:1217:1217))
        (PORT d[4] (965:965:965) (927:927:927))
        (PORT d[5] (966:966:966) (927:927:927))
        (PORT d[6] (3366:3366:3366) (3004:3004:3004))
        (PORT d[7] (1046:1046:1046) (995:995:995))
        (PORT d[8] (981:981:981) (943:943:943))
        (PORT d[9] (1327:1327:1327) (1233:1233:1233))
        (PORT d[10] (1090:1090:1090) (1039:1039:1039))
        (PORT d[11] (1315:1315:1315) (1192:1192:1192))
        (PORT clk (1818:1818:1818) (1881:1881:1881))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (230:230:230))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1818:1818:1818) (1881:1881:1881))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (1819:1819:1819) (1882:1882:1882))
        (IOPATH (posedge clk) pulse (0:0:0) (2853:2853:2853))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1773:1773:1773) (1834:1834:1834))
        (IOPATH (posedge clk) q (355:355:355) (355:355:355))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (56:56:56))
      (HOLD d (posedge clk) (190:190:190))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (845:845:845) (868:868:868))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (846:846:846) (869:869:869))
        (IOPATH (posedge clk) pulse (0:0:0) (3162:3162:3162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1918:1918:1918) (1642:1642:1642))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1442:1442:1442) (1487:1487:1487))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1496:1496:1496) (1423:1423:1423))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1980:1980:1980) (1677:1677:1677))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1442:1442:1442) (1487:1487:1487))
        (PORT d (90:90:90) (101:101:101))
        (PORT clrn (1496:1496:1496) (1423:1423:1423))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
        (IOPATH (negedge clrn) q (222:222:222) (222:222:222))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (826:826:826) (765:765:765))
        (PORT dataa[1] (854:854:854) (775:775:775))
        (PORT dataa[2] (821:821:821) (763:763:763))
        (PORT dataa[3] (898:898:898) (822:822:822))
        (PORT dataa[4] (833:833:833) (772:772:772))
        (PORT dataa[5] (822:822:822) (760:760:760))
        (PORT dataa[6] (857:857:857) (783:783:783))
        (PORT dataa[7] (922:922:922) (889:889:889))
        (PORT dataa[8] (1196:1196:1196) (1072:1072:1072))
        (PORT dataa[9] (1228:1228:1228) (1108:1108:1108))
        (PORT dataa[10] (481:481:481) (466:466:466))
        (PORT dataa[11] (477:477:477) (464:464:464))
        (PORT dataa[12] (861:861:861) (803:803:803))
        (PORT dataa[13] (1037:1037:1037) (1179:1179:1179))
        (PORT dataa[14] (843:843:843) (910:910:910))
        (PORT dataa[15] (851:851:851) (916:916:916))
        (PORT dataa[16] (1037:1037:1037) (1179:1179:1179))
        (PORT dataa[17] (843:843:843) (910:910:910))
        (PORT datab[6] (833:833:833) (778:778:778))
        (PORT datab[7] (1087:1087:1087) (968:968:968))
        (PORT datab[8] (877:877:877) (802:802:802))
        (PORT datab[9] (1175:1175:1175) (1048:1048:1048))
        (PORT datab[10] (862:862:862) (801:801:801))
        (PORT datab[11] (844:844:844) (767:767:767))
        (PORT datab[12] (873:873:873) (792:792:792))
        (PORT datab[13] (876:876:876) (805:805:805))
        (PORT datab[14] (893:893:893) (831:831:831))
        (PORT datab[15] (845:845:845) (745:745:745))
        (PORT datab[16] (811:811:811) (869:869:869))
        (PORT datab[17] (957:957:957) (1080:1080:1080))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (494:494:494) (489:489:489))
        (PORT dataa[7] (798:798:798) (701:701:701))
        (PORT dataa[8] (500:500:500) (490:490:490))
        (PORT dataa[9] (523:523:523) (507:507:507))
        (PORT dataa[10] (759:759:759) (691:691:691))
        (PORT dataa[11] (532:532:532) (518:518:518))
        (PORT dataa[12] (490:490:490) (488:488:488))
        (PORT dataa[13] (528:528:528) (518:518:518))
        (PORT dataa[14] (786:786:786) (711:711:711))
        (PORT dataa[15] (519:519:519) (506:506:506))
        (PORT dataa[16] (496:496:496) (502:502:502))
        (PORT dataa[17] (707:707:707) (797:797:797))
        (PORT datab[10] (791:791:791) (884:884:884))
        (PORT datab[11] (584:584:584) (583:583:583))
        (PORT datab[12] (593:593:593) (589:589:589))
        (PORT datab[13] (791:791:791) (884:884:884))
        (PORT datab[14] (584:584:584) (583:583:583))
        (PORT datab[15] (593:593:593) (589:589:589))
        (PORT datab[16] (791:791:791) (884:884:884))
        (PORT datab[17] (584:584:584) (583:583:583))
        (IOPATH dataa dataout (3554:3554:3554) (3554:3554:3554))
        (IOPATH datab dataout (3476:3476:3476) (3476:3476:3476))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (124:124:124) (132:132:132))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1114:1114:1114) (897:897:897))
        (PORT datab (1397:1397:1397) (1123:1123:1123))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1139:1139:1139) (906:906:906))
        (PORT datab (1117:1117:1117) (895:895:895))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1383:1383:1383) (1107:1107:1107))
        (PORT datab (1019:1019:1019) (837:837:837))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1159:1159:1159) (948:948:948))
        (PORT datab (1062:1062:1062) (863:863:863))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1147:1147:1147) (933:933:933))
        (PORT datab (1071:1071:1071) (857:857:857))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1033:1033:1033) (857:857:857))
        (PORT datab (1427:1427:1427) (1152:1152:1152))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (438:438:438) (455:455:455))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1112:1112:1112) (893:893:893))
        (PORT datab (1379:1379:1379) (1125:1125:1125))
        (IOPATH dataa combout (435:435:435) (407:407:407))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (1028:1028:1028) (836:836:836))
        (PORT datad (1115:1115:1115) (908:908:908))
        (IOPATH datab combout (437:437:437) (451:451:451))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
)
