module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);

//DAC904闂備焦鐪归崝宀€鈧凹鍨冲▎銏ゎ敇閵忕姷顦╅梺鎯х箺椤寮抽埡鍛厱婵☆垯璀﹂崕鎾寸箾閺夋垶顥㈡鐐村灴婵偓闁绘ɑ褰冪徊楣冩倵濞堝灝鏋涘Δ鐘茬箳濡383 - DAC_DATA) * 闂備焦瀵ч惌16383闂備焦瀵х粙鎴βㄩ埀顒傜磼鏉堛劎绠撴い顐犲灲婵″爼宕ㄩ鐐电ɑ闁诲孩顔栭崰鏍崲濡厧鍨濇い鏍ㄧ矋婵ジ鏌曢崼婵嗩伀闁荤喐绻傞湁闁挎繂鐗婄涵鍫曟煛娴ｉ潧鈧牕顭囪箛娑樼闁告劕寮堕惁鏃堟⒑鐞涒€充壕闂佽鍨庢担閿嬫崳缂傚倸鍊搁崐鐢稿疾濞戙垺鍎嶆い鎺戝閺咁剛鈧厜鍋撻柍褜鍓熼幃锟狀敇閻樺啿纾銈嗙墬瀹€绋款瀶椤曗偓閺

wire	CLK_20M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// 闂備礁鎼崐浠嬶綖婢跺奔鐒婃繝闈涱儏閺嬩線鏌ｅΔ鈧悧鍡欑矈
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

//闂傚倷绀侀妵姗€鎳楃捄濂借寰勯幇顓炰虎濡炪倖鐗楁稉瑙勭瑹閳ь剙鐣
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// 婵犳鍠楃换鎰緤閼恒儯鍋婇柍銉︽灱閺嬫牠鏌曡箛濠傚⒉婵炲牆鐖奸弻鐔烘嫚閳ヨ櫕鐝紓浣介哺缁辨湠M闂
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// 濠电偞鍨堕幐鎼佀囬幎鏂ょ稏婵°倐鍋撻柍缁樻婵偓闁斥晛鍟宥夋⒑绾拋鍤冮柛锝庡灣濡叉劕顭抽崠鐜€闂
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

//闂備線娼婚梽鍕熆濡警鐒介柛鎰典簼椤洘銇勯弽銊ㄥ妞ゎ偓缍侀弻
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

//闂備礁婀遍…鍫ニ囬悽绋挎瀬妞ゆ挶鍨圭粻宕囨喐瀹ュ鏄ユ俊銈傚亾閾伙綁鏌嶉妷銊︾彧闁
key_con u_key_con(
	.clk			(CLK_20M	),
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);

//婵犵數鍋涢ˇ顓㈠垂瑜版帗鍤嬮柣妤€鐗婄紞鍥煙閹冩毐婵絽顦靛娲敃閵忊晜效闁诲孩鍝庨崝宀冪亽闂佺偨鍎村▍鏇㈠磻
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;
// 楂樼簿搴﹀箙搴︽帶鍒- 浣跨敤瀹氱偣杩愮畻閬垮厤闄ゆ硶鎴柇璇樊
localparam [7:0] SPI_VV = 8'd1;  // 琛板噺绯绘暟
localparam [15:0] SCALE_FACTOR = 16'd21845;  // 65536/3锛岀敤浜/3缂╂斁鐨勫畾鐐逛箻娉

// 鏂规硶1锛氱洿鎺ュ畾鐐逛箻娉曠缉鏀撅紙鎺ㄨ崘锛
wire [29:0] scaled_data;                           // 14浣脳 16浣= 30浣
assign scaled_data = DATA_BUF * SCALE_FACTOR;      
assign DAC_DATA = scaled_data[29:16];              // 鍙栭珮14浣嶏紝瀹炵幇绮剧‘鐨/3缂╂斁

// 鏂规硶2锛氬鏋滈渶瑕佷繚鎸佸師鏈夌殑鍋忕疆閫昏緫锛屽彲浠ョ敤杩欎釜鏇寸簿纭殑鐗堟湰
// wire [29:0] scaled_signal;
// wire [29:0] scaled_offset; 
// assign scaled_signal = DATA_BUF * SCALE_FACTOR;
// assign scaled_offset = 14'd8191 * SCALE_FACTOR;
// assign DAC_DATA = scaled_signal[29:16] + (14'd8191 - scaled_offset[29:16]); 
//assign DAC_DATA = 14'd10000; // 闂備胶鍎甸弲娑㈡偤閵娧呯焾闁挎洍鍋撻棁澶愭倵閿濆骸骞樻俊鍙夋倐閺V闁诲骸缍婂鑽ょ不閹达絻浜

endmodule
