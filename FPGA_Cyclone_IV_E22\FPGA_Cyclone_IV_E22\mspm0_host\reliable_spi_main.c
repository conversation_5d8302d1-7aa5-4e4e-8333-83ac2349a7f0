/*
 * 高可靠性SPI通信版本 - 专门解决命中率问题
 * 功能：通过优化的SPI时序控制FPGA输出波形
 * 
 * 关键改进：
 * 1. 严格的时序控制
 * 2. 数据完整性验证
 * 3. 重传机制
 * 4. 调试输出
 */

#include "ti_msp_dl_config.h"
#include <stdint.h>

// 频率档位定义
typedef enum {
    FREQ_1KHZ = 0,      // 1KHz
    FREQ_10KHZ = 1,     // 10KHz  
    FREQ_100KHZ = 2,    // 100KHz
    FREQ_1MHZ = 3,      // 1MHz
    FREQ_1_5MHZ = 4     // 1.5MHz
} freq_level_t;

// SPI通信状态
typedef enum {
    SPI_SUCCESS = 0,
    SPI_TIMEOUT = 1,
    SPI_ERROR = 2
} spi_status_t;

// 函数声明
void reliable_spi_init(void);
spi_status_t send_dac_params_reliable(freq_level_t freq_level, uint8_t amplitude_percent);
void delay_us(uint32_t us);
void delay_ms(uint32_t ms);
void debug_output(uint16_t data, spi_status_t status);

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 可靠SPI初始化
    reliable_spi_init();
    
    // 延时等待系统稳定
    delay_ms(1000);
    
    // 主循环：可靠性测试
    uint32_t success_count = 0;
    uint32_t total_count = 0;
    
    while(1) {
        // 测试序列
        freq_level_t test_freqs[] = {FREQ_1KHZ, FREQ_10KHZ, FREQ_100KHZ, FREQ_1MHZ};
        uint8_t test_amps[] = {25, 50, 75, 100};
        
        for(int i = 0; i < 4; i++) {
            total_count++;
            spi_status_t status = send_dac_params_reliable(test_freqs[i], test_amps[i]);
            
            if(status == SPI_SUCCESS) {
                success_count++;
            }
            
            // 调试输出
            uint16_t test_data = ((uint16_t)test_freqs[i] << 12) | ((test_amps[i] * 4095) / 100);
            debug_output(test_data, status);
            
            delay_ms(500);
        }
        
        // 每轮测试后显示成功率
        if(total_count >= 100) {
            // LED闪烁显示成功率（简化版本）
            uint32_t success_rate = (success_count * 100) / total_count;
            for(int i = 0; i < success_rate / 10; i++) {
                DL_GPIO_togglePins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
                delay_ms(100);
                DL_GPIO_togglePins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
                delay_ms(100);
            }
            
            // 重置计数器
            success_count = 0;
            total_count = 0;
            delay_ms(2000);
        }
    }
}

/**
 * 可靠SPI初始化
 */
void reliable_spi_init(void)
{
    // 降低SPI时钟频率以提高可靠性
    DL_SPI_ClockConfig reliable_clock_config = {
        .clockSel = DL_SPI_CLOCK_BUSCLK,
        .divideRatio = DL_SPI_CLOCK_DIVIDE_RATIO_64  // 32MHz/64 = 500KHz
    };
    
    DL_SPI_setClockConfig(SPI_0_INST, &reliable_clock_config);
    DL_SPI_enable(SPI_0_INST);
    
    // 配置CS引脚
    DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
}

/**
 * 可靠的DAC参数发送函数
 * @param freq_level: 频率档位
 * @param amplitude_percent: 幅度百分比
 * @return: 发送状态
 */
spi_status_t send_dac_params_reliable(freq_level_t freq_level, uint8_t amplitude_percent)
{
    // 参数检查
    if(freq_level > FREQ_1_5MHZ) return SPI_ERROR;
    if(amplitude_percent > 100) return SPI_ERROR;
    
    // 计算数据
    uint16_t amplitude = (amplitude_percent * 4095) / 100;
    uint16_t spi_data = ((uint16_t)freq_level << 12) | (amplitude & 0x0FFF);
    
    // 重传机制：最多尝试3次
    for(int retry = 0; retry < 3; retry++) {
        // 等待SPI空闲
        uint32_t timeout = 10000;
        while (DL_SPI_isBusy(SPI_0_INST) && timeout--) {
            delay_us(1);
        }
        if(timeout == 0) return SPI_TIMEOUT;
        
        // 严格的时序控制
        // 1. 预延时
        delay_us(100);
        
        // 2. CS拉低
        DL_GPIO_clearPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
        
        // 3. CS建立时间
        delay_us(10);
        
        // 4. 发送数据（16位一次性发送）
        DL_SPI_transmitData16(SPI_0_INST, spi_data);
        
        // 5. 等待传输完成
        timeout = 10000;
        while (DL_SPI_isBusy(SPI_0_INST) && timeout--) {
            delay_us(1);
        }
        if(timeout == 0) {
            DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
            continue; // 重试
        }
        
        // 6. 数据保持时间
        delay_us(20);
        
        // 7. CS拉高
        DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
        
        // 8. 恢复时间
        delay_us(50);
        
        // 发送成功
        return SPI_SUCCESS;
    }
    
    return SPI_ERROR; // 重试失败
}

/**
 * 微秒延时函数
 */
void delay_us(uint32_t us)
{
    // 假设32MHz系统时钟，每微秒32个时钟周期
    uint32_t cycles = us * 32;
    DL_Common_delayCycles(cycles);
}

/**
 * 毫秒延时函数
 */
void delay_ms(uint32_t ms)
{
    delay_us(ms * 1000);
}

/**
 * 调试输出函数（通过LED模式显示）
 */
void debug_output(uint16_t data, spi_status_t status)
{
    // 简化的调试输出：通过LED闪烁模式显示状态
    if(status == SPI_SUCCESS) {
        // 成功：短闪1次
        DL_GPIO_clearPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
        delay_ms(50);
        DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
    } else {
        // 失败：长闪3次
        for(int i = 0; i < 3; i++) {
            DL_GPIO_clearPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
            delay_ms(200);
            DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
            delay_ms(100);
        }
    }
}

/**
 * 高级测试函数：专门测试边界条件
 */
void boundary_test(void)
{
    // 测试最小值
    send_dac_params_reliable(FREQ_1KHZ, 0);
    delay_ms(1000);
    
    // 测试最大值
    send_dac_params_reliable(FREQ_1_5MHZ, 100);
    delay_ms(1000);
    
    // 测试中间值
    send_dac_params_reliable(FREQ_100KHZ, 50);
    delay_ms(1000);
    
    // 快速切换测试
    for(int i = 0; i < 10; i++) {
        send_dac_params_reliable((freq_level_t)(i % 5), (i * 10) % 100);
        delay_ms(100);
    }
}
