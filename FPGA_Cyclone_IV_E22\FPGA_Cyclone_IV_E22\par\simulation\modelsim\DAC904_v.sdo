// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2V, and temperature 85 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 07:25:27")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1288:1288:1288) (1302:1302:1302))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1731:1731:1731) (1557:1557:1557))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1908:1908:1908) (1656:1656:1656))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1933:1933:1933) (1649:1649:1649))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1444:1444:1444) (1308:1308:1308))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1982:1982:1982) (1716:1716:1716))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1901:1901:1901) (1619:1619:1619))
        (IOPATH i o (4518:4518:4518) (4585:4585:4585))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1363:1363:1363) (1181:1181:1181))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1365:1365:1365) (1186:1186:1186))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1271:1271:1271) (1097:1097:1097))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1304:1304:1304) (1139:1139:1139))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1733:1733:1733) (1523:1523:1523))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (2151:2151:2151) (1914:1914:1914))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1854:1854:1854) (1710:1710:1710))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (1861:1861:1861) (2086:2086:2086))
        (IOPATH i o (3029:3029:3029) (3068:3068:3068))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (1313:1313:1313) (1313:1313:1313))
        (PORT inclk[0] (2313:2313:2313) (2313:2313:2313))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (2338:2338:2338) (2305:2305:2305))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1525:1525:1525) (1639:1639:1639))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_d)
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT asdata (2002:2002:2002) (2085:2085:2085))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1585:1585:1585) (1673:1673:1673))
        (PORT datad (3770:3770:3770) (3982:3982:3982))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (520:520:520) (547:547:547))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT asdata (983:983:983) (1009:1009:1009))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT asdata (787:787:787) (856:856:856))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT asdata (814:814:814) (884:884:884))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (325:325:325) (396:396:396))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (324:324:324) (394:394:394))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (514:514:514) (545:545:545))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (323:323:323) (394:394:394))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (348:348:348) (420:420:420))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (387:387:387) (465:465:465))
        (IOPATH datab combout (494:494:494) (496:496:496))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1603:1603:1603) (1645:1645:1645))
        (PORT asdata (787:787:787) (857:857:857))
        (PORT clrn (1656:1656:1656) (1606:1606:1606))
        (PORT ena (1263:1263:1263) (1194:1194:1194))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1651:1651:1651))
        (PORT asdata (2147:2147:2147) (2081:2081:2081))
        (PORT clrn (1681:1681:1681) (1634:1634:1634))
        (PORT ena (2473:2473:2473) (2327:2327:2327))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[13\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (439:439:439) (582:582:582))
        (IOPATH datac combout (324:324:324) (315:315:315))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1681:1681:1681) (1634:1634:1634))
        (PORT ena (2473:2473:2473) (2327:2327:2327))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1651:1651:1651))
        (PORT asdata (1022:1022:1022) (1043:1043:1043))
        (PORT clrn (1681:1681:1681) (1634:1634:1634))
        (PORT ena (2473:2473:2473) (2327:2327:2327))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1619:1619:1619) (1651:1651:1651))
        (PORT asdata (1012:1012:1012) (1031:1031:1031))
        (PORT clrn (1681:1681:1681) (1634:1634:1634))
        (PORT ena (2473:2473:2473) (2327:2327:2327))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (633:633:633))
        (PORT datab (469:469:469) (629:629:629))
        (PORT datac (433:433:433) (575:575:575))
        (PORT datad (661:661:661) (703:703:703))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1088:1088:1088) (1102:1102:1102))
        (PORT datab (1078:1078:1078) (1078:1078:1078))
        (PORT datac (610:610:610) (647:647:647))
        (PORT datad (990:990:990) (994:994:994))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1084:1084:1084) (1097:1097:1097))
        (PORT datab (1076:1076:1076) (1076:1076:1076))
        (PORT datac (617:617:617) (655:655:655))
        (PORT datad (985:985:985) (988:988:988))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (472:472:472) (632:632:632))
        (PORT datab (471:471:471) (632:632:632))
        (PORT datac (437:437:437) (579:579:579))
        (PORT datad (664:664:664) (707:707:707))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (635:635:635))
        (PORT datab (466:466:466) (624:624:624))
        (PORT datac (430:430:430) (570:570:570))
        (PORT datad (658:658:658) (700:700:700))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (470:470:470) (630:630:630))
        (PORT datab (475:475:475) (637:637:637))
        (PORT datac (442:442:442) (585:585:585))
        (PORT datad (668:668:668) (711:711:711))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (637:637:637))
        (PORT datab (463:463:463) (621:621:621))
        (PORT datac (426:426:426) (566:566:566))
        (PORT datad (655:655:655) (697:697:697))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (636:636:636))
        (PORT datab (465:465:465) (623:623:623))
        (PORT datac (428:428:428) (569:569:569))
        (PORT datad (657:657:657) (699:699:699))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (470:470:470) (631:631:631))
        (PORT datab (474:474:474) (636:636:636))
        (PORT datac (441:441:441) (584:584:584))
        (PORT datad (667:667:667) (710:710:710))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (471:471:471) (631:631:631))
        (PORT datab (473:473:473) (634:634:634))
        (PORT datac (438:438:438) (581:581:581))
        (PORT datad (665:665:665) (708:708:708))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (635:635:635))
        (PORT datab (466:466:466) (625:625:625))
        (PORT datac (430:430:430) (571:571:571))
        (PORT datad (659:659:659) (701:701:701))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (471:471:471) (631:631:631))
        (PORT datab (473:473:473) (635:635:635))
        (PORT datac (439:439:439) (582:582:582))
        (PORT datad (666:666:666) (709:709:709))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (455:455:455) (412:412:412))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (472:472:472) (633:633:633))
        (PORT datab (469:469:469) (630:630:630))
        (PORT datac (435:435:435) (576:576:576))
        (PORT datad (662:662:662) (704:704:704))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (475:475:475) (636:636:636))
        (PORT datab (464:464:464) (622:622:622))
        (PORT datac (427:427:427) (567:567:567))
        (PORT datad (656:656:656) (697:697:697))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH datab combout (437:437:437) (436:436:436))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (472:472:472) (621:621:621))
        (PORT datab (593:593:593) (620:620:620))
        (PORT datad (653:653:653) (694:694:694))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (360:360:360) (444:444:444))
        (PORT datab (459:459:459) (616:616:616))
        (PORT datad (432:432:432) (575:575:575))
        (IOPATH dataa combout (421:421:421) (418:418:418))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (473:473:473) (622:622:622))
        (PORT datab (461:461:461) (619:619:619))
        (PORT datad (431:431:431) (574:574:574))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1893:1893:1893) (1741:1741:1741))
        (PORT datab (331:331:331) (406:406:406))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1270:1270:1270) (1173:1173:1173))
        (PORT datab (331:331:331) (407:407:407))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1624:1624:1624) (1523:1523:1523))
        (PORT datab (331:331:331) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (973:973:973) (918:918:918))
        (PORT datab (547:547:547) (565:565:565))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1700:1700:1700) (1600:1600:1600))
        (PORT datab (332:332:332) (408:408:408))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1617:1617:1617) (1516:1516:1516))
        (PORT datab (547:547:547) (565:565:565))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (857:857:857) (817:817:817))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (974:974:974) (920:920:920))
        (PORT datab (333:333:333) (409:409:409))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (545:545:545) (574:574:574))
        (PORT datab (925:925:925) (890:890:890))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (545:545:545) (578:578:578))
        (PORT datab (918:918:918) (857:857:857))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (928:928:928) (871:871:871))
        (PORT datab (333:333:333) (409:409:409))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (874:874:874) (840:840:840))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (546:546:546) (576:576:576))
        (PORT datab (925:925:925) (890:890:890))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (335:335:335) (418:418:418))
        (PORT datab (877:877:877) (823:823:823))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (934:934:934) (874:874:874))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (461:461:461) (486:486:486))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (455:455:455) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (334:334:334) (417:417:417))
        (PORT datab (1224:1224:1224) (1147:1147:1147))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1617:1617:1617) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1870:1870:1870) (1815:1815:1815))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (606:606:606) (617:617:617))
        (PORT datab (898:898:898) (853:853:853))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (872:872:872) (840:840:840))
        (PORT datab (332:332:332) (407:407:407))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (473:473:473) (489:489:489))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (930:930:930) (880:880:880))
        (PORT datab (332:332:332) (408:408:408))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (336:336:336) (419:419:419))
        (PORT datab (970:970:970) (916:916:916))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (555:555:555) (601:601:601))
        (PORT datab (945:945:945) (889:889:889))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (1591:1591:1591) (1457:1457:1457))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (929:929:929) (878:878:878))
        (PORT datab (555:555:555) (594:594:594))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (566:566:566) (601:601:601))
        (PORT datab (970:970:970) (916:916:916))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1089:1089:1089) (1104:1104:1104))
        (PORT datab (1078:1078:1078) (1078:1078:1078))
        (PORT datac (609:609:609) (646:646:646))
        (PORT datad (991:991:991) (995:995:995))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datac combout (327:327:327) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (569:569:569) (611:611:611))
        (PORT datab (840:840:840) (797:797:797))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1331:1331:1331) (1324:1324:1324))
        (PORT datab (976:976:976) (916:916:916))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1085:1085:1085) (1099:1099:1099))
        (PORT datab (1077:1077:1077) (1076:1076:1076))
        (PORT datac (616:616:616) (653:653:653))
        (PORT datad (986:986:986) (990:990:990))
        (IOPATH dataa combout (404:404:404) (398:398:398))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (558:558:558) (592:592:592))
        (PORT datab (901:901:901) (862:862:862))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (387:387:387) (471:471:471))
        (PORT datab (969:969:969) (915:915:915))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (595:595:595) (634:634:634))
        (PORT datab (946:946:946) (891:891:891))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1085:1085:1085) (1098:1098:1098))
        (PORT datab (1077:1077:1077) (1076:1076:1076))
        (PORT datac (616:616:616) (654:654:654))
        (PORT datad (986:986:986) (989:989:989))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (920:920:920) (863:863:863))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1086:1086:1086) (1100:1100:1100))
        (PORT datab (1078:1078:1078) (1077:1077:1077))
        (PORT datac (615:615:615) (652:652:652))
        (PORT datad (987:987:987) (991:991:991))
        (IOPATH dataa combout (405:405:405) (398:398:398))
        (IOPATH datab combout (407:407:407) (408:408:408))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (959:959:959) (893:893:893))
        (PORT datab (384:384:384) (461:461:461))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (448:448:448))
        (IOPATH dataa combout (471:471:471) (481:481:481))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1628:1628:1628) (1667:1667:1667))
        (PORT d (99:99:99) (115:115:115))
        (PORT sclr (1878:1878:1878) (1828:1828:1828))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD sclr (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1360:1360:1360) (1347:1347:1347))
        (PORT d[1] (1432:1432:1432) (1409:1409:1409))
        (PORT d[2] (1374:1374:1374) (1352:1352:1352))
        (PORT d[3] (1383:1383:1383) (1368:1368:1368))
        (PORT d[4] (1045:1045:1045) (1055:1055:1055))
        (PORT d[5] (990:990:990) (1004:1004:1004))
        (PORT d[6] (3119:3119:3119) (2936:2936:2936))
        (PORT d[7] (1131:1131:1131) (1140:1140:1140))
        (PORT d[8] (1072:1072:1072) (1081:1081:1081))
        (PORT d[9] (1800:1800:1800) (1751:1751:1751))
        (PORT d[10] (1058:1058:1058) (1067:1067:1067))
        (PORT d[11] (1699:1699:1699) (1620:1620:1620))
        (PORT clk (2031:2031:2031) (2080:2080:2080))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2031:2031:2031) (2080:2080:2080))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2032:2032:2032) (2081:2081:2081))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1983:1983:1983) (2033:2033:2033))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (955:955:955) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1313:1313:1313) (1209:1209:1209))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1627:1627:1627) (1664:1664:1664))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1694:1694:1694) (1642:1642:1642))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1270:1270:1270) (1187:1187:1187))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1627:1627:1627) (1664:1664:1664))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1694:1694:1694) (1642:1642:1642))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1420:1420:1420) (1388:1388:1388))
        (PORT d[1] (1819:1819:1819) (1749:1749:1749))
        (PORT d[2] (1345:1345:1345) (1327:1327:1327))
        (PORT d[3] (1789:1789:1789) (1744:1744:1744))
        (PORT d[4] (987:987:987) (998:998:998))
        (PORT d[5] (1029:1029:1029) (1035:1035:1035))
        (PORT d[6] (3494:3494:3494) (3284:3284:3284))
        (PORT d[7] (1030:1030:1030) (1035:1035:1035))
        (PORT d[8] (1009:1009:1009) (1020:1020:1020))
        (PORT d[9] (1740:1740:1740) (1692:1692:1692))
        (PORT d[10] (1044:1044:1044) (1049:1049:1049))
        (PORT d[11] (1362:1362:1362) (1309:1309:1309))
        (PORT clk (2034:2034:2034) (2084:2084:2084))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2034:2034:2034) (2084:2084:2084))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2035:2035:2035) (2085:2085:2085))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1986:1986:1986) (2037:2037:2037))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (957:957:957) (978:978:978))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (979:979:979))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1306:1306:1306) (1218:1218:1218))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1605:1605:1605) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1660:1660:1660) (1608:1608:1608))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1605:1605:1605) (1646:1646:1646))
        (PORT asdata (2016:2016:2016) (1877:1877:1877))
        (PORT clrn (1660:1660:1660) (1608:1608:1608))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1732:1732:1732) (1680:1680:1680))
        (PORT d[1] (1842:1842:1842) (1772:1772:1772))
        (PORT d[2] (1807:1807:1807) (1737:1737:1737))
        (PORT d[3] (1451:1451:1451) (1427:1427:1427))
        (PORT d[4] (1012:1012:1012) (1026:1026:1026))
        (PORT d[5] (1037:1037:1037) (1047:1047:1047))
        (PORT d[6] (3027:3027:3027) (2845:2845:2845))
        (PORT d[7] (1109:1109:1109) (1119:1119:1119))
        (PORT d[8] (1080:1080:1080) (1090:1090:1090))
        (PORT d[9] (1442:1442:1442) (1413:1413:1413))
        (PORT d[10] (1108:1108:1108) (1114:1114:1114))
        (PORT d[11] (1384:1384:1384) (1332:1332:1332))
        (PORT clk (2042:2042:2042) (2096:2096:2096))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2042:2042:2042) (2096:2096:2096))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2043:2043:2043) (2097:2097:2097))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1994:1994:1994) (2049:2049:2049))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (965:965:965) (990:990:990))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (966:966:966) (991:991:991))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (966:966:966) (991:991:991))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (966:966:966) (991:991:991))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1596:1596:1596) (1468:1468:1468))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1631:1631:1631))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1661:1661:1661) (1610:1610:1610))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1595:1595:1595) (1631:1631:1631))
        (PORT asdata (2056:2056:2056) (1918:1918:1918))
        (PORT clrn (1661:1661:1661) (1610:1610:1610))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1336:1336:1336) (1322:1322:1322))
        (PORT d[1] (1435:1435:1435) (1406:1406:1406))
        (PORT d[2] (1333:1333:1333) (1315:1315:1315))
        (PORT d[3] (1406:1406:1406) (1384:1384:1384))
        (PORT d[4] (1011:1011:1011) (1025:1025:1025))
        (PORT d[5] (1393:1393:1393) (1378:1378:1378))
        (PORT d[6] (3081:3081:3081) (2898:2898:2898))
        (PORT d[7] (1849:1849:1849) (1795:1795:1795))
        (PORT d[8] (1039:1039:1039) (1051:1051:1051))
        (PORT d[9] (1401:1401:1401) (1377:1377:1377))
        (PORT d[10] (1137:1137:1137) (1143:1143:1143))
        (PORT d[11] (1377:1377:1377) (1325:1325:1325))
        (PORT clk (2043:2043:2043) (2098:2098:2098))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2043:2043:2043) (2098:2098:2098))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2044:2044:2044) (2099:2099:2099))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1995:1995:1995) (2051:2051:2051))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (966:966:966) (992:992:992))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1324:1324:1324) (1237:1237:1237))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1593:1593:1593) (1628:1628:1628))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1658:1658:1658) (1608:1608:1608))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (987:987:987) (927:927:927))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1601:1601:1601) (1643:1643:1643))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1654:1654:1654) (1604:1604:1604))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1744:1744:1744) (1694:1694:1694))
        (PORT d[1] (1734:1734:1734) (1682:1682:1682))
        (PORT d[2] (1781:1781:1781) (1719:1719:1719))
        (PORT d[3] (1784:1784:1784) (1725:1725:1725))
        (PORT d[4] (1456:1456:1456) (1428:1428:1428))
        (PORT d[5] (1380:1380:1380) (1364:1364:1364))
        (PORT d[6] (2628:2628:2628) (2477:2477:2477))
        (PORT d[7] (1490:1490:1490) (1467:1467:1467))
        (PORT d[8] (1487:1487:1487) (1460:1460:1460))
        (PORT d[9] (1771:1771:1771) (1718:1718:1718))
        (PORT d[10] (1549:1549:1549) (1518:1518:1518))
        (PORT d[11] (1456:1456:1456) (1402:1402:1402))
        (PORT clk (2044:2044:2044) (2098:2098:2098))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2044:2044:2044) (2098:2098:2098))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2045:2045:2045) (2099:2099:2099))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1996:1996:1996) (2051:2051:2051))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (992:992:992))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (993:993:993))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (993:993:993))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1310:1310:1310) (1220:1220:1220))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1340:1340:1340) (1249:1249:1249))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1647:1647:1647))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1677:1677:1677) (1629:1629:1629))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1777:1777:1777) (1728:1728:1728))
        (PORT d[1] (1033:1033:1033) (1023:1023:1023))
        (PORT d[2] (973:973:973) (967:967:967))
        (PORT d[3] (1800:1800:1800) (1757:1757:1757))
        (PORT d[4] (1389:1389:1389) (1359:1359:1359))
        (PORT d[5] (1371:1371:1371) (1345:1345:1345))
        (PORT d[6] (1472:1472:1472) (1434:1434:1434))
        (PORT d[7] (1022:1022:1022) (1002:1002:1002))
        (PORT d[8] (1007:1007:1007) (999:999:999))
        (PORT d[9] (1721:1721:1721) (1675:1675:1675))
        (PORT d[10] (987:987:987) (975:975:975))
        (PORT d[11] (1388:1388:1388) (1330:1330:1330))
        (PORT clk (2044:2044:2044) (2099:2099:2099))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2044:2044:2044) (2099:2099:2099))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2045:2045:2045) (2100:2100:2100))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1996:1996:1996) (2052:2052:2052))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (967:967:967) (993:993:993))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (994:994:994))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (994:994:994))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (968:968:968) (994:994:994))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1625:1625:1625) (1499:1499:1499))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1606:1606:1606) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1676:1676:1676) (1621:1621:1621))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1647:1647:1647) (1588:1588:1588))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1606:1606:1606) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1676:1676:1676) (1621:1621:1621))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (1398:1398:1398) (1375:1375:1375))
        (PORT d[1] (1433:1433:1433) (1395:1395:1395))
        (PORT d[2] (1395:1395:1395) (1378:1378:1378))
        (PORT d[3] (1361:1361:1361) (1341:1341:1341))
        (PORT d[4] (1005:1005:1005) (1017:1017:1017))
        (PORT d[5] (1006:1006:1006) (1017:1017:1017))
        (PORT d[6] (3529:3529:3529) (3318:3318:3318))
        (PORT d[7] (1088:1088:1088) (1092:1092:1092))
        (PORT d[8] (1026:1026:1026) (1037:1037:1037))
        (PORT d[9] (1381:1381:1381) (1359:1359:1359))
        (PORT d[10] (1135:1135:1135) (1140:1140:1140))
        (PORT d[11] (1370:1370:1370) (1318:1318:1318))
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (254:254:254))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2036:2036:2036) (2082:2082:2082))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (2037:2037:2037) (2083:2083:2083))
        (IOPATH (posedge clk) pulse (0:0:0) (3134:3134:3134))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (1988:1988:1988) (2035:2035:2035))
        (IOPATH (posedge clk) q (392:392:392) (392:392:392))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (64:64:64))
      (HOLD d (posedge clk) (211:211:211))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (959:959:959) (976:976:976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (960:960:960) (977:977:977))
        (IOPATH (posedge clk) pulse (0:0:0) (3469:3469:3469))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (1992:1992:1992) (1834:1834:1834))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1608:1608:1608) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1660:1660:1660) (1609:1609:1609))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (2058:2058:2058) (1873:1873:1873))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (1608:1608:1608) (1648:1648:1648))
        (PORT d (99:99:99) (115:115:115))
        (PORT clrn (1660:1660:1660) (1609:1609:1609))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
        (IOPATH (negedge clrn) q (247:247:247) (247:247:247))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (864:864:864) (849:849:849))
        (PORT dataa[1] (887:887:887) (859:859:859))
        (PORT dataa[2] (855:855:855) (847:847:847))
        (PORT dataa[3] (929:929:929) (914:914:914))
        (PORT dataa[4] (868:868:868) (856:856:856))
        (PORT dataa[5] (855:855:855) (844:844:844))
        (PORT dataa[6] (887:887:887) (872:872:872))
        (PORT dataa[7] (969:969:969) (982:982:982))
        (PORT dataa[8] (1245:1245:1245) (1190:1190:1190))
        (PORT dataa[9] (1279:1279:1279) (1232:1232:1232))
        (PORT dataa[10] (502:502:502) (516:516:516))
        (PORT dataa[11] (496:496:496) (514:514:514))
        (PORT dataa[12] (896:896:896) (895:895:895))
        (PORT dataa[13] (1158:1158:1158) (1226:1226:1226))
        (PORT dataa[14] (938:938:938) (951:951:951))
        (PORT dataa[15] (948:948:948) (956:956:956))
        (PORT dataa[16] (1158:1158:1158) (1226:1226:1226))
        (PORT dataa[17] (938:938:938) (951:951:951))
        (PORT datab[6] (872:872:872) (868:868:868))
        (PORT datab[7] (1135:1135:1135) (1082:1082:1082))
        (PORT datab[8] (916:916:916) (894:894:894))
        (PORT datab[9] (1221:1221:1221) (1167:1167:1167))
        (PORT datab[10] (902:902:902) (894:894:894))
        (PORT datab[11] (887:887:887) (855:855:855))
        (PORT datab[12] (908:908:908) (884:884:884))
        (PORT datab[13] (914:914:914) (898:898:898))
        (PORT datab[14] (937:937:937) (926:926:926))
        (PORT datab[15] (879:879:879) (826:826:826))
        (PORT datab[16] (905:905:905) (912:912:912))
        (PORT datab[17] (1070:1070:1070) (1125:1125:1125))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (520:520:520) (544:544:544))
        (PORT dataa[7] (830:830:830) (782:782:782))
        (PORT dataa[8] (530:530:530) (544:544:544))
        (PORT dataa[9] (552:552:552) (562:562:562))
        (PORT dataa[10] (794:794:794) (771:771:771))
        (PORT dataa[11] (561:561:561) (576:576:576))
        (PORT dataa[12] (515:515:515) (543:543:543))
        (PORT dataa[13] (556:556:556) (575:575:575))
        (PORT dataa[14] (819:819:819) (789:789:789))
        (PORT dataa[15] (548:548:548) (561:561:561))
        (PORT dataa[16] (552:552:552) (532:532:532))
        (PORT dataa[17] (788:788:788) (825:825:825))
        (PORT datab[10] (885:885:885) (914:914:914))
        (PORT datab[11] (649:649:649) (619:619:619))
        (PORT datab[12] (659:659:659) (624:624:624))
        (PORT datab[13] (885:885:885) (914:914:914))
        (PORT datab[14] (649:649:649) (619:619:619))
        (PORT datab[15] (659:659:659) (624:624:624))
        (PORT datab[16] (885:885:885) (914:914:914))
        (PORT datab[17] (649:649:649) (619:619:619))
        (IOPATH dataa dataout (3928:3928:3928) (3928:3928:3928))
        (IOPATH datab dataout (3863:3863:3863) (3863:3863:3863))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (139:139:139) (148:148:148))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1131:1131:1131) (1006:1006:1006))
        (PORT datab (1433:1433:1433) (1262:1262:1262))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1154:1154:1154) (1024:1024:1024))
        (PORT datab (1128:1128:1128) (1005:1005:1005))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1406:1406:1406) (1248:1248:1248))
        (PORT datab (1039:1039:1039) (940:940:940))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1177:1177:1177) (1068:1068:1068))
        (PORT datab (1082:1082:1082) (971:971:971))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1163:1163:1163) (1056:1056:1056))
        (PORT datab (1083:1083:1083) (968:968:968))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1048:1048:1048) (964:964:964))
        (PORT datab (1458:1458:1458) (1297:1297:1297))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (494:494:494) (496:496:496))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (1127:1127:1127) (1001:1001:1001))
        (PORT datab (1407:1407:1407) (1266:1266:1266))
        (IOPATH dataa combout (471:471:471) (453:453:453))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (1049:1049:1049) (941:941:941))
        (PORT datad (1137:1137:1137) (1022:1022:1022))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
)
