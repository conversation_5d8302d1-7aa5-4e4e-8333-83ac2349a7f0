// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.

// VENDOR "Altera"
// PROGRAM "Quartus II 64-Bit"
// VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition"

// DATE "08/01/2025 10:17:21"

// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

// 
// This Verilog file should be used for ModelSim-Altera (Verilog) only
// 

`timescale 1 ps/ 1 ps

module DAC904_TOP (
	SYS_CLK,
	SYS_RST,
	KEY_IN,
	spi_sclk,
	spi_mosi,
	spi_cs_n,
	PD,
	DAC_CLK,
	DAC_DATA);
input 	SYS_CLK;
input 	SYS_RST;
input 	[2:0] KEY_IN;
input 	spi_sclk;
input 	spi_mosi;
input 	spi_cs_n;
output 	PD;
output 	DAC_CLK;
output 	[13:0] DAC_DATA;

// Design Ports Information
// KEY_IN[0]	=>  Location: PIN_90,	 I/O Standard: 2.5 V,	 Current Strength: Default
// KEY_IN[1]	=>  Location: PIN_89,	 I/O Standard: 2.5 V,	 Current Strength: Default
// KEY_IN[2]	=>  Location: PIN_91,	 I/O Standard: 2.5 V,	 Current Strength: Default
// PD	=>  Location: PIN_103,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_CLK	=>  Location: PIN_101,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[0]	=>  Location: PIN_73,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[1]	=>  Location: PIN_74,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[2]	=>  Location: PIN_75,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[3]	=>  Location: PIN_76,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[4]	=>  Location: PIN_77,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[5]	=>  Location: PIN_80,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[6]	=>  Location: PIN_83,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[7]	=>  Location: PIN_84,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[8]	=>  Location: PIN_85,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[9]	=>  Location: PIN_86,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[10]	=>  Location: PIN_87,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[11]	=>  Location: PIN_98,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[12]	=>  Location: PIN_99,	 I/O Standard: 2.5 V,	 Current Strength: Default
// DAC_DATA[13]	=>  Location: PIN_100,	 I/O Standard: 2.5 V,	 Current Strength: Default
// SYS_RST	=>  Location: PIN_24,	 I/O Standard: 2.5 V,	 Current Strength: Default
// SYS_CLK	=>  Location: PIN_88,	 I/O Standard: 2.5 V,	 Current Strength: Default
// spi_mosi	=>  Location: PIN_25,	 I/O Standard: 2.5 V,	 Current Strength: Default
// spi_sclk	=>  Location: PIN_23,	 I/O Standard: 2.5 V,	 Current Strength: Default
// spi_cs_n	=>  Location: PIN_31,	 I/O Standard: 2.5 V,	 Current Strength: Default


wire gnd;
wire vcc;
wire unknown;

assign gnd = 1'b0;
assign vcc = 1'b1;
assign unknown = 1'bx;

tri1 devclrn;
tri1 devpor;
tri1 devoe;
// synopsys translate_off
initial $sdf_annotate("DAC904_min_1200mv_0c_v_fast.sdo");
// synopsys translate_on

wire \Mult0|auto_generated|mac_out2~DATAOUT26 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT27 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT28 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT29 ;
wire \Mult0|auto_generated|mac_out2~0 ;
wire \Mult0|auto_generated|mac_out2~1 ;
wire \Mult0|auto_generated|mac_out2~2 ;
wire \Mult0|auto_generated|mac_out2~3 ;
wire \Mult0|auto_generated|mac_out2~4 ;
wire \Mult0|auto_generated|mac_out2~5 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT8 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT9 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT10 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT11 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT12 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT13 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT14 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT15 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT16 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT17 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT18 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT19 ;
wire \Mult0|auto_generated|mac_out4~0 ;
wire \Mult0|auto_generated|mac_out4~1 ;
wire \Mult0|auto_generated|mac_out4~2 ;
wire \Mult0|auto_generated|mac_out4~3 ;
wire \Mult0|auto_generated|mac_out4~4 ;
wire \Mult0|auto_generated|mac_out4~5 ;
wire \Mult0|auto_generated|mac_out4~6 ;
wire \Mult0|auto_generated|mac_out4~7 ;
wire \Mult0|auto_generated|mac_out4~8 ;
wire \Mult0|auto_generated|mac_out4~9 ;
wire \Mult0|auto_generated|mac_out4~10 ;
wire \Mult0|auto_generated|mac_out4~11 ;
wire \Mult0|auto_generated|mac_out4~12 ;
wire \Mult0|auto_generated|mac_out4~13 ;
wire \Mult0|auto_generated|mac_out4~14 ;
wire \Mult0|auto_generated|mac_out4~15 ;
wire \KEY_IN[0]~input_o ;
wire \KEY_IN[1]~input_o ;
wire \KEY_IN[2]~input_o ;
wire \SYS_RST~input_o ;
wire \SYS_RST~inputclkctrl_outclk ;
wire \SYS_CLK~input_o ;
wire \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_fbout ;
wire \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ;
wire \spi_mosi~input_o ;
wire \spi_data[0]~feeder_combout ;
wire \spi_sclk~input_o ;
wire \sclk_d~q ;
wire \spi_cs_n~input_o ;
wire \always1~0_combout ;
wire \spi_data[1]~feeder_combout ;
wire \spi_data[5]~feeder_combout ;
wire \spi_data[6]~feeder_combout ;
wire \spi_data[7]~feeder_combout ;
wire \spi_data[8]~feeder_combout ;
wire \spi_data[9]~feeder_combout ;
wire \spi_data[10]~0_combout ;
wire \spi_data[13]~1_combout ;
wire \WideOr2~0_combout ;
wire \WideOr3~0_combout ;
wire \WideOr8~0_combout ;
wire \WideOr13~0_combout ;
wire \WideOr14~0_combout ;
wire \WideOr15~0_combout ;
wire \WideOr16~0_combout ;
wire \WideOr17~0_combout ;
wire \WideOr19~0_combout ;
wire \WideOr20~0_combout ;
wire \WideOr23~0_combout ;
wire \WideOr24~0_combout ;
wire \WideOr5~0_combout ;
wire \WideOr26~0_combout ;
wire \WideOr28~0_combout ;
wire \WideOr9~0_combout ;
wire \WideOr30~0_combout ;
wire \u_add_32bit|add[0]~32_combout ;
wire \u_add_32bit|add[0]~33 ;
wire \u_add_32bit|add[1]~34_combout ;
wire \u_add_32bit|add[1]~35 ;
wire \u_add_32bit|add[2]~36_combout ;
wire \u_add_32bit|add[2]~37 ;
wire \u_add_32bit|add[3]~38_combout ;
wire \u_add_32bit|add[3]~39 ;
wire \u_add_32bit|add[4]~40_combout ;
wire \u_add_32bit|add[4]~41 ;
wire \u_add_32bit|add[5]~42_combout ;
wire \u_add_32bit|add[5]~43 ;
wire \u_add_32bit|add[6]~44_combout ;
wire \u_add_32bit|add[6]~45 ;
wire \u_add_32bit|add[7]~46_combout ;
wire \u_add_32bit|add[7]~47 ;
wire \u_add_32bit|add[8]~48_combout ;
wire \u_add_32bit|add[8]~49 ;
wire \u_add_32bit|add[9]~50_combout ;
wire \u_add_32bit|add[9]~51 ;
wire \u_add_32bit|add[10]~52_combout ;
wire \u_add_32bit|add[10]~53 ;
wire \u_add_32bit|add[11]~54_combout ;
wire \u_add_32bit|add[11]~55 ;
wire \u_add_32bit|add[12]~56_combout ;
wire \u_add_32bit|add[12]~57 ;
wire \u_add_32bit|add[13]~58_combout ;
wire \u_add_32bit|add[13]~59 ;
wire \u_add_32bit|add[14]~60_combout ;
wire \u_add_32bit|add[14]~61 ;
wire \u_add_32bit|add[15]~62_combout ;
wire \u_add_32bit|add[15]~63 ;
wire \u_add_32bit|add[16]~64_combout ;
wire \u_add_32bit|add[16]~65 ;
wire \u_add_32bit|add[17]~66_combout ;
wire \u_add_32bit|add[17]~67 ;
wire \u_add_32bit|add[18]~68_combout ;
wire \u_add_32bit|add[18]~69 ;
wire \u_add_32bit|add[19]~70_combout ;
wire \u_add_32bit|add[19]~71 ;
wire \u_add_32bit|add[20]~72_combout ;
wire \u_add_32bit|add[20]~73 ;
wire \u_add_32bit|add[21]~74_combout ;
wire \u_add_32bit|add[21]~75 ;
wire \u_add_32bit|add[22]~76_combout ;
wire \u_add_32bit|add[22]~77 ;
wire \u_add_32bit|add[23]~78_combout ;
wire \WideOr6~0_combout ;
wire \u_add_32bit|add[23]~79 ;
wire \u_add_32bit|add[24]~80_combout ;
wire \u_add_32bit|add[24]~81 ;
wire \u_add_32bit|add[25]~82_combout ;
wire \WideOr4~0_combout ;
wire \u_add_32bit|add[25]~83 ;
wire \u_add_32bit|add[26]~84_combout ;
wire \u_add_32bit|add[26]~85 ;
wire \u_add_32bit|add[27]~86_combout ;
wire \u_add_32bit|add[27]~87 ;
wire \u_add_32bit|add[28]~88_combout ;
wire \WideOr1~0_combout ;
wire \u_add_32bit|add[28]~89 ;
wire \u_add_32bit|add[29]~90_combout ;
wire \WideOr0~0_combout ;
wire \u_add_32bit|add[29]~91 ;
wire \u_add_32bit|add[30]~92_combout ;
wire \u_add_32bit|add[30]~93 ;
wire \u_add_32bit|add[31]~94_combout ;
wire \u_sel_wave|da_out_reg[0]~feeder_combout ;
wire \u_sel_wave|da_out_reg[1]~feeder_combout ;
wire \u_sel_wave|da_out_reg[2]~feeder_combout ;
wire \u_sel_wave|da_out_reg[4]~feeder_combout ;
wire \u_sel_wave|da_out_reg[6]~feeder_combout ;
wire \u_sel_wave|da_out_reg[7]~feeder_combout ;
wire \u_sel_wave|da_out_reg[8]~feeder_combout ;
wire \u_sel_wave|da_out_reg[9]~feeder_combout ;
wire \u_sel_wave|da_out_reg[10]~feeder_combout ;
wire \u_sel_wave|da_out_reg[11]~feeder_combout ;
wire \u_sel_wave|da_out_reg[12]~feeder_combout ;
wire \u_sel_wave|da_out_reg[13]~feeder_combout ;
wire \Mult0|auto_generated|mac_mult1~dataout ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT1 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT2 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT3 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT4 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT5 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT6 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT7 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT8 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT9 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT10 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT11 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT12 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT13 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT14 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT15 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT16 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT17 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT18 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT19 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT20 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT21 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT22 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT23 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT24 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT25 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT26 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT27 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT28 ;
wire \Mult0|auto_generated|mac_mult1~DATAOUT29 ;
wire \Mult0|auto_generated|mac_mult1~0 ;
wire \Mult0|auto_generated|mac_mult1~1 ;
wire \Mult0|auto_generated|mac_mult1~2 ;
wire \Mult0|auto_generated|mac_mult1~3 ;
wire \Mult0|auto_generated|mac_mult1~4 ;
wire \Mult0|auto_generated|mac_mult1~5 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT18 ;
wire \Mult0|auto_generated|mac_mult3~dataout ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT1 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT2 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT3 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT4 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT5 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT6 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT7 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT8 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT9 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT10 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT11 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT12 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT13 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT14 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT15 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT16 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT17 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT18 ;
wire \Mult0|auto_generated|mac_mult3~DATAOUT19 ;
wire \Mult0|auto_generated|mac_mult3~0 ;
wire \Mult0|auto_generated|mac_mult3~1 ;
wire \Mult0|auto_generated|mac_mult3~2 ;
wire \Mult0|auto_generated|mac_mult3~3 ;
wire \Mult0|auto_generated|mac_mult3~4 ;
wire \Mult0|auto_generated|mac_mult3~5 ;
wire \Mult0|auto_generated|mac_mult3~6 ;
wire \Mult0|auto_generated|mac_mult3~7 ;
wire \Mult0|auto_generated|mac_mult3~8 ;
wire \Mult0|auto_generated|mac_mult3~9 ;
wire \Mult0|auto_generated|mac_mult3~10 ;
wire \Mult0|auto_generated|mac_mult3~11 ;
wire \Mult0|auto_generated|mac_mult3~12 ;
wire \Mult0|auto_generated|mac_mult3~13 ;
wire \Mult0|auto_generated|mac_mult3~14 ;
wire \Mult0|auto_generated|mac_mult3~15 ;
wire \Mult0|auto_generated|mac_out4~dataout ;
wire \Mult0|auto_generated|op_1~0_combout ;
wire \Mult0|auto_generated|mac_out4~DATAOUT1 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT19 ;
wire \Mult0|auto_generated|op_1~1 ;
wire \Mult0|auto_generated|op_1~2_combout ;
wire \Mult0|auto_generated|mac_out2~DATAOUT20 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT2 ;
wire \Mult0|auto_generated|op_1~3 ;
wire \Mult0|auto_generated|op_1~4_combout ;
wire \Mult0|auto_generated|mac_out2~DATAOUT21 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT3 ;
wire \Mult0|auto_generated|op_1~5 ;
wire \Mult0|auto_generated|op_1~6_combout ;
wire \Mult0|auto_generated|mac_out2~DATAOUT22 ;
wire \Mult0|auto_generated|mac_out4~DATAOUT4 ;
wire \Mult0|auto_generated|op_1~7 ;
wire \Mult0|auto_generated|op_1~8_combout ;
wire \Mult0|auto_generated|mac_out4~DATAOUT5 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT23 ;
wire \Mult0|auto_generated|op_1~9 ;
wire \Mult0|auto_generated|op_1~10_combout ;
wire \Mult0|auto_generated|mac_out4~DATAOUT6 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT24 ;
wire \Mult0|auto_generated|op_1~11 ;
wire \Mult0|auto_generated|op_1~12_combout ;
wire \Mult0|auto_generated|mac_out4~DATAOUT7 ;
wire \Mult0|auto_generated|mac_out2~DATAOUT25 ;
wire \Mult0|auto_generated|op_1~13 ;
wire \Mult0|auto_generated|op_1~14_combout ;
wire [13:0] \u_ROM_Sin|altsyncram_component|auto_generated|q_a ;
wire [31:0] \u_add_32bit|add ;
wire [13:0] \u_sel_wave|da_out_reg ;
wire [4:0] \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk ;
wire [15:0] spi_data;
wire [38:0] \Mult0|auto_generated|w187w ;

wire [4:0] \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus ;
wire [35:0] \Mult0|auto_generated|mac_out2_DATAOUT_bus ;
wire [35:0] \Mult0|auto_generated|mac_out4_DATAOUT_bus ;
wire [35:0] \Mult0|auto_generated|mac_mult1_DATAOUT_bus ;
wire [35:0] \Mult0|auto_generated|mac_mult3_DATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10_PORTADATAOUT_bus ;
wire [1:0] \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12_PORTADATAOUT_bus ;

assign \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [0] = \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus [0];
assign \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [1] = \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus [1];
assign \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [2] = \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus [2];
assign \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [3] = \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus [3];
assign \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [4] = \u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus [4];

assign \Mult0|auto_generated|mac_out2~0  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [0];
assign \Mult0|auto_generated|mac_out2~1  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [1];
assign \Mult0|auto_generated|mac_out2~2  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [2];
assign \Mult0|auto_generated|mac_out2~3  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [3];
assign \Mult0|auto_generated|mac_out2~4  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [4];
assign \Mult0|auto_generated|mac_out2~5  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [5];
assign \Mult0|auto_generated|w187w [0] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [6];
assign \Mult0|auto_generated|w187w [1] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [7];
assign \Mult0|auto_generated|w187w [2] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [8];
assign \Mult0|auto_generated|w187w [3] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [9];
assign \Mult0|auto_generated|w187w [4] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [10];
assign \Mult0|auto_generated|w187w [5] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [11];
assign \Mult0|auto_generated|w187w [6] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [12];
assign \Mult0|auto_generated|w187w [7] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [13];
assign \Mult0|auto_generated|w187w [8] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [14];
assign \Mult0|auto_generated|w187w [9] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [15];
assign \Mult0|auto_generated|w187w [10] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [16];
assign \Mult0|auto_generated|w187w [11] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [17];
assign \Mult0|auto_generated|w187w [12] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [18];
assign \Mult0|auto_generated|w187w [13] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [19];
assign \Mult0|auto_generated|w187w [14] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [20];
assign \Mult0|auto_generated|w187w [15] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [21];
assign \Mult0|auto_generated|w187w [16] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [22];
assign \Mult0|auto_generated|w187w [17] = \Mult0|auto_generated|mac_out2_DATAOUT_bus [23];
assign \Mult0|auto_generated|mac_out2~DATAOUT18  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [24];
assign \Mult0|auto_generated|mac_out2~DATAOUT19  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [25];
assign \Mult0|auto_generated|mac_out2~DATAOUT20  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [26];
assign \Mult0|auto_generated|mac_out2~DATAOUT21  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [27];
assign \Mult0|auto_generated|mac_out2~DATAOUT22  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [28];
assign \Mult0|auto_generated|mac_out2~DATAOUT23  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [29];
assign \Mult0|auto_generated|mac_out2~DATAOUT24  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [30];
assign \Mult0|auto_generated|mac_out2~DATAOUT25  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [31];
assign \Mult0|auto_generated|mac_out2~DATAOUT26  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [32];
assign \Mult0|auto_generated|mac_out2~DATAOUT27  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [33];
assign \Mult0|auto_generated|mac_out2~DATAOUT28  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [34];
assign \Mult0|auto_generated|mac_out2~DATAOUT29  = \Mult0|auto_generated|mac_out2_DATAOUT_bus [35];

assign \Mult0|auto_generated|mac_out4~0  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [0];
assign \Mult0|auto_generated|mac_out4~1  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [1];
assign \Mult0|auto_generated|mac_out4~2  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [2];
assign \Mult0|auto_generated|mac_out4~3  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [3];
assign \Mult0|auto_generated|mac_out4~4  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [4];
assign \Mult0|auto_generated|mac_out4~5  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [5];
assign \Mult0|auto_generated|mac_out4~6  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [6];
assign \Mult0|auto_generated|mac_out4~7  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [7];
assign \Mult0|auto_generated|mac_out4~8  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [8];
assign \Mult0|auto_generated|mac_out4~9  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [9];
assign \Mult0|auto_generated|mac_out4~10  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [10];
assign \Mult0|auto_generated|mac_out4~11  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [11];
assign \Mult0|auto_generated|mac_out4~12  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [12];
assign \Mult0|auto_generated|mac_out4~13  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [13];
assign \Mult0|auto_generated|mac_out4~14  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [14];
assign \Mult0|auto_generated|mac_out4~15  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [15];
assign \Mult0|auto_generated|mac_out4~dataout  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [16];
assign \Mult0|auto_generated|mac_out4~DATAOUT1  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [17];
assign \Mult0|auto_generated|mac_out4~DATAOUT2  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [18];
assign \Mult0|auto_generated|mac_out4~DATAOUT3  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [19];
assign \Mult0|auto_generated|mac_out4~DATAOUT4  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [20];
assign \Mult0|auto_generated|mac_out4~DATAOUT5  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [21];
assign \Mult0|auto_generated|mac_out4~DATAOUT6  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [22];
assign \Mult0|auto_generated|mac_out4~DATAOUT7  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [23];
assign \Mult0|auto_generated|mac_out4~DATAOUT8  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [24];
assign \Mult0|auto_generated|mac_out4~DATAOUT9  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [25];
assign \Mult0|auto_generated|mac_out4~DATAOUT10  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [26];
assign \Mult0|auto_generated|mac_out4~DATAOUT11  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [27];
assign \Mult0|auto_generated|mac_out4~DATAOUT12  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [28];
assign \Mult0|auto_generated|mac_out4~DATAOUT13  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [29];
assign \Mult0|auto_generated|mac_out4~DATAOUT14  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [30];
assign \Mult0|auto_generated|mac_out4~DATAOUT15  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [31];
assign \Mult0|auto_generated|mac_out4~DATAOUT16  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [32];
assign \Mult0|auto_generated|mac_out4~DATAOUT17  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [33];
assign \Mult0|auto_generated|mac_out4~DATAOUT18  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [34];
assign \Mult0|auto_generated|mac_out4~DATAOUT19  = \Mult0|auto_generated|mac_out4_DATAOUT_bus [35];

assign \Mult0|auto_generated|mac_mult1~0  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [0];
assign \Mult0|auto_generated|mac_mult1~1  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [1];
assign \Mult0|auto_generated|mac_mult1~2  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [2];
assign \Mult0|auto_generated|mac_mult1~3  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [3];
assign \Mult0|auto_generated|mac_mult1~4  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [4];
assign \Mult0|auto_generated|mac_mult1~5  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [5];
assign \Mult0|auto_generated|mac_mult1~dataout  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [6];
assign \Mult0|auto_generated|mac_mult1~DATAOUT1  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [7];
assign \Mult0|auto_generated|mac_mult1~DATAOUT2  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [8];
assign \Mult0|auto_generated|mac_mult1~DATAOUT3  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [9];
assign \Mult0|auto_generated|mac_mult1~DATAOUT4  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [10];
assign \Mult0|auto_generated|mac_mult1~DATAOUT5  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [11];
assign \Mult0|auto_generated|mac_mult1~DATAOUT6  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [12];
assign \Mult0|auto_generated|mac_mult1~DATAOUT7  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [13];
assign \Mult0|auto_generated|mac_mult1~DATAOUT8  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [14];
assign \Mult0|auto_generated|mac_mult1~DATAOUT9  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [15];
assign \Mult0|auto_generated|mac_mult1~DATAOUT10  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [16];
assign \Mult0|auto_generated|mac_mult1~DATAOUT11  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [17];
assign \Mult0|auto_generated|mac_mult1~DATAOUT12  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [18];
assign \Mult0|auto_generated|mac_mult1~DATAOUT13  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [19];
assign \Mult0|auto_generated|mac_mult1~DATAOUT14  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [20];
assign \Mult0|auto_generated|mac_mult1~DATAOUT15  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [21];
assign \Mult0|auto_generated|mac_mult1~DATAOUT16  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [22];
assign \Mult0|auto_generated|mac_mult1~DATAOUT17  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [23];
assign \Mult0|auto_generated|mac_mult1~DATAOUT18  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [24];
assign \Mult0|auto_generated|mac_mult1~DATAOUT19  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [25];
assign \Mult0|auto_generated|mac_mult1~DATAOUT20  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [26];
assign \Mult0|auto_generated|mac_mult1~DATAOUT21  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [27];
assign \Mult0|auto_generated|mac_mult1~DATAOUT22  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [28];
assign \Mult0|auto_generated|mac_mult1~DATAOUT23  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [29];
assign \Mult0|auto_generated|mac_mult1~DATAOUT24  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [30];
assign \Mult0|auto_generated|mac_mult1~DATAOUT25  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [31];
assign \Mult0|auto_generated|mac_mult1~DATAOUT26  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [32];
assign \Mult0|auto_generated|mac_mult1~DATAOUT27  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [33];
assign \Mult0|auto_generated|mac_mult1~DATAOUT28  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [34];
assign \Mult0|auto_generated|mac_mult1~DATAOUT29  = \Mult0|auto_generated|mac_mult1_DATAOUT_bus [35];

assign \Mult0|auto_generated|mac_mult3~0  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [0];
assign \Mult0|auto_generated|mac_mult3~1  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [1];
assign \Mult0|auto_generated|mac_mult3~2  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [2];
assign \Mult0|auto_generated|mac_mult3~3  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [3];
assign \Mult0|auto_generated|mac_mult3~4  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [4];
assign \Mult0|auto_generated|mac_mult3~5  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [5];
assign \Mult0|auto_generated|mac_mult3~6  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [6];
assign \Mult0|auto_generated|mac_mult3~7  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [7];
assign \Mult0|auto_generated|mac_mult3~8  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [8];
assign \Mult0|auto_generated|mac_mult3~9  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [9];
assign \Mult0|auto_generated|mac_mult3~10  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [10];
assign \Mult0|auto_generated|mac_mult3~11  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [11];
assign \Mult0|auto_generated|mac_mult3~12  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [12];
assign \Mult0|auto_generated|mac_mult3~13  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [13];
assign \Mult0|auto_generated|mac_mult3~14  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [14];
assign \Mult0|auto_generated|mac_mult3~15  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [15];
assign \Mult0|auto_generated|mac_mult3~dataout  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [16];
assign \Mult0|auto_generated|mac_mult3~DATAOUT1  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [17];
assign \Mult0|auto_generated|mac_mult3~DATAOUT2  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [18];
assign \Mult0|auto_generated|mac_mult3~DATAOUT3  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [19];
assign \Mult0|auto_generated|mac_mult3~DATAOUT4  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [20];
assign \Mult0|auto_generated|mac_mult3~DATAOUT5  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [21];
assign \Mult0|auto_generated|mac_mult3~DATAOUT6  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [22];
assign \Mult0|auto_generated|mac_mult3~DATAOUT7  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [23];
assign \Mult0|auto_generated|mac_mult3~DATAOUT8  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [24];
assign \Mult0|auto_generated|mac_mult3~DATAOUT9  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [25];
assign \Mult0|auto_generated|mac_mult3~DATAOUT10  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [26];
assign \Mult0|auto_generated|mac_mult3~DATAOUT11  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [27];
assign \Mult0|auto_generated|mac_mult3~DATAOUT12  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [28];
assign \Mult0|auto_generated|mac_mult3~DATAOUT13  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [29];
assign \Mult0|auto_generated|mac_mult3~DATAOUT14  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [30];
assign \Mult0|auto_generated|mac_mult3~DATAOUT15  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [31];
assign \Mult0|auto_generated|mac_mult3~DATAOUT16  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [32];
assign \Mult0|auto_generated|mac_mult3~DATAOUT17  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [33];
assign \Mult0|auto_generated|mac_mult3~DATAOUT18  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [34];
assign \Mult0|auto_generated|mac_mult3~DATAOUT19  = \Mult0|auto_generated|mac_mult3_DATAOUT_bus [35];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [0] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [1] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [2] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [3] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [4] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [5] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [6] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [7] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [8] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [9] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [10] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [11] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10_PORTADATAOUT_bus [1];

assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [12] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12_PORTADATAOUT_bus [0];
assign \u_ROM_Sin|altsyncram_component|auto_generated|q_a [13] = \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12_PORTADATAOUT_bus [1];

// Location: IOOBUF_X34_Y18_N16
cycloneive_io_obuf \PD~output (
	.i(gnd),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(PD),
	.obar());
// synopsys translate_off
defparam \PD~output .bus_hold = "false";
defparam \PD~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y18_N23
cycloneive_io_obuf \DAC_CLK~output (
	.i(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_CLK),
	.obar());
// synopsys translate_off
defparam \DAC_CLK~output .bus_hold = "false";
defparam \DAC_CLK~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y2_N23
cycloneive_io_obuf \DAC_DATA[0]~output (
	.i(\Mult0|auto_generated|w187w [12]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[0]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[0]~output .bus_hold = "false";
defparam \DAC_DATA[0]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y2_N16
cycloneive_io_obuf \DAC_DATA[1]~output (
	.i(\Mult0|auto_generated|w187w [13]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[1]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[1]~output .bus_hold = "false";
defparam \DAC_DATA[1]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y3_N23
cycloneive_io_obuf \DAC_DATA[2]~output (
	.i(\Mult0|auto_generated|w187w [14]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[2]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[2]~output .bus_hold = "false";
defparam \DAC_DATA[2]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y4_N23
cycloneive_io_obuf \DAC_DATA[3]~output (
	.i(\Mult0|auto_generated|w187w [15]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[3]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[3]~output .bus_hold = "false";
defparam \DAC_DATA[3]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y4_N16
cycloneive_io_obuf \DAC_DATA[4]~output (
	.i(\Mult0|auto_generated|w187w [16]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[4]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[4]~output .bus_hold = "false";
defparam \DAC_DATA[4]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y7_N9
cycloneive_io_obuf \DAC_DATA[5]~output (
	.i(\Mult0|auto_generated|w187w [17]),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[5]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[5]~output .bus_hold = "false";
defparam \DAC_DATA[5]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y9_N23
cycloneive_io_obuf \DAC_DATA[6]~output (
	.i(\Mult0|auto_generated|op_1~0_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[6]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[6]~output .bus_hold = "false";
defparam \DAC_DATA[6]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y9_N16
cycloneive_io_obuf \DAC_DATA[7]~output (
	.i(\Mult0|auto_generated|op_1~2_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[7]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[7]~output .bus_hold = "false";
defparam \DAC_DATA[7]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y9_N9
cycloneive_io_obuf \DAC_DATA[8]~output (
	.i(\Mult0|auto_generated|op_1~4_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[8]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[8]~output .bus_hold = "false";
defparam \DAC_DATA[8]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y9_N2
cycloneive_io_obuf \DAC_DATA[9]~output (
	.i(\Mult0|auto_generated|op_1~6_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[9]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[9]~output .bus_hold = "false";
defparam \DAC_DATA[9]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y10_N9
cycloneive_io_obuf \DAC_DATA[10]~output (
	.i(\Mult0|auto_generated|op_1~8_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[10]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[10]~output .bus_hold = "false";
defparam \DAC_DATA[10]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y17_N23
cycloneive_io_obuf \DAC_DATA[11]~output (
	.i(\Mult0|auto_generated|op_1~10_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[11]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[11]~output .bus_hold = "false";
defparam \DAC_DATA[11]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y17_N16
cycloneive_io_obuf \DAC_DATA[12]~output (
	.i(\Mult0|auto_generated|op_1~12_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[12]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[12]~output .bus_hold = "false";
defparam \DAC_DATA[12]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOOBUF_X34_Y17_N2
cycloneive_io_obuf \DAC_DATA[13]~output (
	.i(!\Mult0|auto_generated|op_1~14_combout ),
	.oe(vcc),
	.seriesterminationcontrol(16'b0000000000000000),
	.devoe(devoe),
	.o(DAC_DATA[13]),
	.obar());
// synopsys translate_off
defparam \DAC_DATA[13]~output .bus_hold = "false";
defparam \DAC_DATA[13]~output .open_drain_output = "false";
// synopsys translate_on

// Location: IOIBUF_X0_Y11_N15
cycloneive_io_ibuf \SYS_RST~input (
	.i(SYS_RST),
	.ibar(gnd),
	.o(\SYS_RST~input_o ));
// synopsys translate_off
defparam \SYS_RST~input .bus_hold = "false";
defparam \SYS_RST~input .simulate_z_as = "z";
// synopsys translate_on

// Location: CLKCTRL_G4
cycloneive_clkctrl \SYS_RST~inputclkctrl (
	.ena(vcc),
	.inclk({vcc,vcc,vcc,\SYS_RST~input_o }),
	.clkselect(2'b00),
	.devclrn(devclrn),
	.devpor(devpor),
	.outclk(\SYS_RST~inputclkctrl_outclk ));
// synopsys translate_off
defparam \SYS_RST~inputclkctrl .clock_type = "global clock";
defparam \SYS_RST~inputclkctrl .ena_register_mode = "none";
// synopsys translate_on

// Location: IOIBUF_X34_Y12_N22
cycloneive_io_ibuf \SYS_CLK~input (
	.i(SYS_CLK),
	.ibar(gnd),
	.o(\SYS_CLK~input_o ));
// synopsys translate_off
defparam \SYS_CLK~input .bus_hold = "false";
defparam \SYS_CLK~input .simulate_z_as = "z";
// synopsys translate_on

// Location: PLL_2
cycloneive_pll \u_PLL_CLK|altpll_component|auto_generated|pll1 (
	.areset(!\SYS_RST~inputclkctrl_outclk ),
	.pfdena(vcc),
	.fbin(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_fbout ),
	.phaseupdown(gnd),
	.phasestep(gnd),
	.scandata(gnd),
	.scanclk(gnd),
	.scanclkena(vcc),
	.configupdate(gnd),
	.clkswitch(gnd),
	.inclk({gnd,\SYS_CLK~input_o }),
	.phasecounterselect(3'b000),
	.phasedone(),
	.scandataout(),
	.scandone(),
	.activeclock(),
	.locked(),
	.vcooverrange(),
	.vcounderrange(),
	.fbout(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_fbout ),
	.clk(\u_PLL_CLK|altpll_component|auto_generated|pll1_CLK_bus ),
	.clkbad());
// synopsys translate_off
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .auto_settings = "false";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .bandwidth_type = "medium";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c0_high = 15;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c0_initial = 1;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c0_low = 15;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c0_mode = "even";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c0_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_high = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_initial = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_low = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_mode = "bypass";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c1_use_casc_in = "off";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_high = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_initial = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_low = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_mode = "bypass";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c2_use_casc_in = "off";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_high = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_initial = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_low = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_mode = "bypass";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c3_use_casc_in = "off";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_high = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_initial = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_low = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_mode = "bypass";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .c4_use_casc_in = "off";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .charge_pump_current_bits = 1;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk0_counter = "c0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk0_divide_by = 5;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk0_duty_cycle = 50;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk0_multiply_by = 2;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk0_phase_shift = "0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk1_counter = "unused";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk1_divide_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk1_duty_cycle = 50;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk1_multiply_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk1_phase_shift = "0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk2_counter = "unused";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk2_divide_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk2_duty_cycle = 50;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk2_multiply_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk2_phase_shift = "0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk3_counter = "unused";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk3_divide_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk3_duty_cycle = 50;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk3_multiply_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk3_phase_shift = "0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk4_counter = "unused";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk4_divide_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk4_duty_cycle = 50;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk4_multiply_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .clk4_phase_shift = "0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .compensate_clock = "clock0";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .inclk0_input_frequency = 20000;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .inclk1_input_frequency = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .loop_filter_c_bits = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .loop_filter_r_bits = 27;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .m = 12;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .m_initial = 1;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .m_ph = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .n = 1;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .operation_mode = "normal";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .pfd_max = 200000;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .pfd_min = 3076;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .pll_compensation_delay = 3267;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .self_reset_on_loss_lock = "off";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .simulation_type = "timing";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .switch_over_type = "auto";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_center = 1538;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_divide_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_frequency_control = "auto";
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_max = 3333;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_min = 1538;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_multiply_by = 0;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_phase_shift_step = 208;
defparam \u_PLL_CLK|altpll_component|auto_generated|pll1 .vco_post_scale = 2;
// synopsys translate_on

// Location: CLKCTRL_G8
cycloneive_clkctrl \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl (
	.ena(vcc),
	.inclk({vcc,vcc,vcc,\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk [0]}),
	.clkselect(2'b00),
	.devclrn(devclrn),
	.devpor(devpor),
	.outclk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ));
// synopsys translate_off
defparam \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl .clock_type = "global clock";
defparam \u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl .ena_register_mode = "none";
// synopsys translate_on

// Location: IOIBUF_X0_Y11_N22
cycloneive_io_ibuf \spi_mosi~input (
	.i(spi_mosi),
	.ibar(gnd),
	.o(\spi_mosi~input_o ));
// synopsys translate_off
defparam \spi_mosi~input .bus_hold = "false";
defparam \spi_mosi~input .simulate_z_as = "z";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N26
cycloneive_lcell_comb \spi_data[0]~feeder (
// Equation(s):
// \spi_data[0]~feeder_combout  = \spi_mosi~input_o 

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\spi_mosi~input_o ),
	.cin(gnd),
	.combout(\spi_data[0]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[0]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[0]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: IOIBUF_X0_Y11_N8
cycloneive_io_ibuf \spi_sclk~input (
	.i(spi_sclk),
	.ibar(gnd),
	.o(\spi_sclk~input_o ));
// synopsys translate_off
defparam \spi_sclk~input .bus_hold = "false";
defparam \spi_sclk~input .simulate_z_as = "z";
// synopsys translate_on

// Location: FF_X19_Y5_N25
dffeas sclk_d(
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(\spi_sclk~input_o ),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\sclk_d~q ),
	.prn(vcc));
// synopsys translate_off
defparam sclk_d.is_wysiwyg = "true";
defparam sclk_d.power_up = "low";
// synopsys translate_on

// Location: IOIBUF_X0_Y7_N1
cycloneive_io_ibuf \spi_cs_n~input (
	.i(spi_cs_n),
	.ibar(gnd),
	.o(\spi_cs_n~input_o ));
// synopsys translate_off
defparam \spi_cs_n~input .bus_hold = "false";
defparam \spi_cs_n~input .simulate_z_as = "z";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N24
cycloneive_lcell_comb \always1~0 (
// Equation(s):
// \always1~0_combout  = (\spi_sclk~input_o  & (!\sclk_d~q  & !\spi_cs_n~input_o ))

	.dataa(\spi_sclk~input_o ),
	.datab(gnd),
	.datac(\sclk_d~q ),
	.datad(\spi_cs_n~input_o ),
	.cin(gnd),
	.combout(\always1~0_combout ),
	.cout());
// synopsys translate_off
defparam \always1~0 .lut_mask = 16'h000A;
defparam \always1~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N27
dffeas \spi_data[0] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[0]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[0]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[0] .is_wysiwyg = "true";
defparam \spi_data[0] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N18
cycloneive_lcell_comb \spi_data[1]~feeder (
// Equation(s):
// \spi_data[1]~feeder_combout  = spi_data[0]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[0]),
	.cin(gnd),
	.combout(\spi_data[1]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[1]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[1]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N19
dffeas \spi_data[1] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[1]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[1]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[1] .is_wysiwyg = "true";
defparam \spi_data[1] .power_up = "low";
// synopsys translate_on

// Location: FF_X19_Y5_N23
dffeas \spi_data[2] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[1]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[2]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[2] .is_wysiwyg = "true";
defparam \spi_data[2] .power_up = "low";
// synopsys translate_on

// Location: FF_X19_Y5_N31
dffeas \spi_data[3] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[2]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[3]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[3] .is_wysiwyg = "true";
defparam \spi_data[3] .power_up = "low";
// synopsys translate_on

// Location: FF_X19_Y5_N7
dffeas \spi_data[4] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[3]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[4]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[4] .is_wysiwyg = "true";
defparam \spi_data[4] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N28
cycloneive_lcell_comb \spi_data[5]~feeder (
// Equation(s):
// \spi_data[5]~feeder_combout  = spi_data[4]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[4]),
	.cin(gnd),
	.combout(\spi_data[5]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[5]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[5]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N29
dffeas \spi_data[5] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[5]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[5]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[5] .is_wysiwyg = "true";
defparam \spi_data[5] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N8
cycloneive_lcell_comb \spi_data[6]~feeder (
// Equation(s):
// \spi_data[6]~feeder_combout  = spi_data[5]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[5]),
	.cin(gnd),
	.combout(\spi_data[6]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[6]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[6]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N9
dffeas \spi_data[6] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[6]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[6]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[6] .is_wysiwyg = "true";
defparam \spi_data[6] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N2
cycloneive_lcell_comb \spi_data[7]~feeder (
// Equation(s):
// \spi_data[7]~feeder_combout  = spi_data[6]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[6]),
	.cin(gnd),
	.combout(\spi_data[7]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[7]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[7]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N3
dffeas \spi_data[7] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[7]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[7]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[7] .is_wysiwyg = "true";
defparam \spi_data[7] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N16
cycloneive_lcell_comb \spi_data[8]~feeder (
// Equation(s):
// \spi_data[8]~feeder_combout  = spi_data[7]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[7]),
	.cin(gnd),
	.combout(\spi_data[8]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[8]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[8]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N17
dffeas \spi_data[8] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[8]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[8]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[8] .is_wysiwyg = "true";
defparam \spi_data[8] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N0
cycloneive_lcell_comb \spi_data[9]~feeder (
// Equation(s):
// \spi_data[9]~feeder_combout  = spi_data[8]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(spi_data[8]),
	.cin(gnd),
	.combout(\spi_data[9]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[9]~feeder .lut_mask = 16'hFF00;
defparam \spi_data[9]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N1
dffeas \spi_data[9] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[9]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[9]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[9] .is_wysiwyg = "true";
defparam \spi_data[9] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X19_Y5_N14
cycloneive_lcell_comb \spi_data[10]~0 (
// Equation(s):
// \spi_data[10]~0_combout  = !spi_data[9]

	.dataa(gnd),
	.datab(spi_data[9]),
	.datac(gnd),
	.datad(gnd),
	.cin(gnd),
	.combout(\spi_data[10]~0_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[10]~0 .lut_mask = 16'h3333;
defparam \spi_data[10]~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y5_N15
dffeas \spi_data[10] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[10]~0_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[10]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[10] .is_wysiwyg = "true";
defparam \spi_data[10] .power_up = "low";
// synopsys translate_on

// Location: FF_X19_Y5_N21
dffeas \spi_data[11] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[10]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[11]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[11] .is_wysiwyg = "true";
defparam \spi_data[11] .power_up = "low";
// synopsys translate_on

// Location: FF_X16_Y13_N31
dffeas \spi_data[12] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[11]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[12]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[12] .is_wysiwyg = "true";
defparam \spi_data[12] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N10
cycloneive_lcell_comb \spi_data[13]~1 (
// Equation(s):
// \spi_data[13]~1_combout  = !spi_data[12]

	.dataa(gnd),
	.datab(gnd),
	.datac(spi_data[12]),
	.datad(gnd),
	.cin(gnd),
	.combout(\spi_data[13]~1_combout ),
	.cout());
// synopsys translate_off
defparam \spi_data[13]~1 .lut_mask = 16'h0F0F;
defparam \spi_data[13]~1 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X16_Y13_N11
dffeas \spi_data[13] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\spi_data[13]~1_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[13]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[13] .is_wysiwyg = "true";
defparam \spi_data[13] .power_up = "low";
// synopsys translate_on

// Location: FF_X16_Y13_N29
dffeas \spi_data[14] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[13]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[14]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[14] .is_wysiwyg = "true";
defparam \spi_data[14] .power_up = "low";
// synopsys translate_on

// Location: FF_X16_Y13_N27
dffeas \spi_data[15] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(spi_data[14]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(\always1~0_combout ),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(spi_data[15]),
	.prn(vcc));
// synopsys translate_off
defparam \spi_data[15] .is_wysiwyg = "true";
defparam \spi_data[15] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N14
cycloneive_lcell_comb \WideOr2~0 (
// Equation(s):
// \WideOr2~0_combout  = (spi_data[14] & (spi_data[13] $ (((!spi_data[15]))))) # (!spi_data[14] & (spi_data[15] & (spi_data[13] $ (!spi_data[12]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr2~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr2~0 .lut_mask = 16'hA944;
defparam \WideOr2~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N8
cycloneive_lcell_comb \WideOr3~0 (
// Equation(s):
// \WideOr3~0_combout  = (spi_data[12] & (!spi_data[14] & ((spi_data[15])))) # (!spi_data[12] & (spi_data[14] $ (((spi_data[13] & !spi_data[15])))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr3~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr3~0 .lut_mask = 16'h5A06;
defparam \WideOr3~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N26
cycloneive_lcell_comb \WideOr8~0 (
// Equation(s):
// \WideOr8~0_combout  = (spi_data[13] & (!spi_data[15] & ((!spi_data[12]) # (!spi_data[14])))) # (!spi_data[13] & (spi_data[15] & ((spi_data[14]) # (spi_data[12]))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr8~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr8~0 .lut_mask = 16'h324C;
defparam \WideOr8~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N8
cycloneive_lcell_comb \WideOr13~0 (
// Equation(s):
// \WideOr13~0_combout  = (spi_data[14] & (spi_data[12] $ (((spi_data[15]) # (!spi_data[13]))))) # (!spi_data[14] & ((spi_data[12] & ((spi_data[15]))) # (!spi_data[12] & ((spi_data[13]) # (!spi_data[15])))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr13~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr13~0 .lut_mask = 16'h3E87;
defparam \WideOr13~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N18
cycloneive_lcell_comb \WideOr14~0 (
// Equation(s):
// \WideOr14~0_combout  = (spi_data[14] & (spi_data[13] $ (((spi_data[15]))))) # (!spi_data[14] & (spi_data[12] $ (((spi_data[13]) # (!spi_data[15])))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr14~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr14~0 .lut_mask = 16'h568B;
defparam \WideOr14~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N0
cycloneive_lcell_comb \WideOr15~0 (
// Equation(s):
// \WideOr15~0_combout  = (spi_data[15] & ((spi_data[14] $ (spi_data[12])))) # (!spi_data[15] & ((spi_data[13] & (!spi_data[14])) # (!spi_data[13] & ((!spi_data[12])))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr15~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr15~0 .lut_mask = 16'h3C27;
defparam \WideOr15~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N24
cycloneive_lcell_comb \WideOr16~0 (
// Equation(s):
// \WideOr16~0_combout  = (spi_data[13] & (((spi_data[12]) # (spi_data[15])))) # (!spi_data[13] & ((spi_data[14] & ((!spi_data[15]))) # (!spi_data[14] & (!spi_data[12] & spi_data[15]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr16~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr16~0 .lut_mask = 16'hABE4;
defparam \WideOr16~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N20
cycloneive_lcell_comb \WideOr17~0 (
// Equation(s):
// \WideOr17~0_combout  = (spi_data[14] & (spi_data[12] $ (((spi_data[13] & !spi_data[15]))))) # (!spi_data[14] & ((spi_data[13] & (spi_data[12] & !spi_data[15])) # (!spi_data[13] & (!spi_data[12] & spi_data[15]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr17~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr17~0 .lut_mask = 16'hC168;
defparam \WideOr17~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N2
cycloneive_lcell_comb \WideOr19~0 (
// Equation(s):
// \WideOr19~0_combout  = (spi_data[14] & (!spi_data[12] & ((spi_data[15]) # (!spi_data[13])))) # (!spi_data[14] & ((spi_data[15] & ((spi_data[12]))) # (!spi_data[15] & (spi_data[13]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr19~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr19~0 .lut_mask = 16'h3C26;
defparam \WideOr19~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N6
cycloneive_lcell_comb \WideOr20~0 (
// Equation(s):
// \WideOr20~0_combout  = (spi_data[12] & ((spi_data[15] & (spi_data[13])) # (!spi_data[15] & ((spi_data[14]))))) # (!spi_data[12] & (spi_data[15] $ (((!spi_data[13] & spi_data[14])))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr20~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr20~0 .lut_mask = 16'hABC4;
defparam \WideOr20~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N16
cycloneive_lcell_comb \WideOr23~0 (
// Equation(s):
// \WideOr23~0_combout  = (spi_data[14] & ((spi_data[12]) # ((spi_data[13] & !spi_data[15])))) # (!spi_data[14] & ((spi_data[12] & (spi_data[13] & !spi_data[15])) # (!spi_data[12] & ((spi_data[15])))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr23~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr23~0 .lut_mask = 16'hC3E8;
defparam \WideOr23~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N4
cycloneive_lcell_comb \WideOr24~0 (
// Equation(s):
// \WideOr24~0_combout  = (spi_data[13] & (((spi_data[12]) # (spi_data[15])))) # (!spi_data[13] & ((spi_data[14] & ((!spi_data[15]))) # (!spi_data[14] & (!spi_data[12]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr24~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr24~0 .lut_mask = 16'hABE5;
defparam \WideOr24~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N12
cycloneive_lcell_comb \WideOr5~0 (
// Equation(s):
// \WideOr5~0_combout  = (spi_data[14] & (spi_data[12] $ (((spi_data[13] & !spi_data[15]))))) # (!spi_data[14] & (!spi_data[13] & (!spi_data[12] & spi_data[15])))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr5~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr5~0 .lut_mask = 16'hC148;
defparam \WideOr5~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N22
cycloneive_lcell_comb \WideOr26~0 (
// Equation(s):
// \WideOr26~0_combout  = (spi_data[13] & ((spi_data[14] & ((!spi_data[15]))) # (!spi_data[14] & (!spi_data[12])))) # (!spi_data[13] & (spi_data[15] & ((spi_data[14]) # (spi_data[12]))))

	.dataa(spi_data[13]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr26~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr26~0 .lut_mask = 16'h568A;
defparam \WideOr26~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N28
cycloneive_lcell_comb \WideOr28~0 (
// Equation(s):
// \WideOr28~0_combout  = (spi_data[13] & ((spi_data[15]) # ((spi_data[12] & spi_data[14])))) # (!spi_data[13] & ((spi_data[14] & ((!spi_data[15]))) # (!spi_data[14] & (!spi_data[12]))))

	.dataa(spi_data[12]),
	.datab(spi_data[13]),
	.datac(spi_data[14]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr28~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr28~0 .lut_mask = 16'hCDB1;
defparam \WideOr28~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N30
cycloneive_lcell_comb \WideOr9~0 (
// Equation(s):
// \WideOr9~0_combout  = (spi_data[14] & (spi_data[12] $ (((!spi_data[15] & spi_data[13]))))) # (!spi_data[14] & (((!spi_data[12] & !spi_data[13]))))

	.dataa(spi_data[15]),
	.datab(spi_data[14]),
	.datac(spi_data[12]),
	.datad(spi_data[13]),
	.cin(gnd),
	.combout(\WideOr9~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr9~0 .lut_mask = 16'h84C3;
defparam \WideOr9~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y13_N26
cycloneive_lcell_comb \WideOr30~0 (
// Equation(s):
// \WideOr30~0_combout  = (spi_data[12] & (spi_data[15] $ (((spi_data[14] & !spi_data[13]))))) # (!spi_data[12] & (((spi_data[14] & spi_data[15])) # (!spi_data[13])))

	.dataa(spi_data[12]),
	.datab(spi_data[14]),
	.datac(spi_data[15]),
	.datad(spi_data[13]),
	.cin(gnd),
	.combout(\WideOr30~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr30~0 .lut_mask = 16'hE07D;
defparam \WideOr30~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N0
cycloneive_lcell_comb \u_add_32bit|add[0]~32 (
// Equation(s):
// \u_add_32bit|add[0]~32_combout  = (\WideOr30~0_combout  & (\u_add_32bit|add [0] $ (VCC))) # (!\WideOr30~0_combout  & (\u_add_32bit|add [0] & VCC))
// \u_add_32bit|add[0]~33  = CARRY((\WideOr30~0_combout  & \u_add_32bit|add [0]))

	.dataa(\WideOr30~0_combout ),
	.datab(\u_add_32bit|add [0]),
	.datac(gnd),
	.datad(vcc),
	.cin(gnd),
	.combout(\u_add_32bit|add[0]~32_combout ),
	.cout(\u_add_32bit|add[0]~33 ));
// synopsys translate_off
defparam \u_add_32bit|add[0]~32 .lut_mask = 16'h6688;
defparam \u_add_32bit|add[0]~32 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X16_Y11_N1
dffeas \u_add_32bit|add[0] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[0]~32_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [0]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[0] .is_wysiwyg = "true";
defparam \u_add_32bit|add[0] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N2
cycloneive_lcell_comb \u_add_32bit|add[1]~34 (
// Equation(s):
// \u_add_32bit|add[1]~34_combout  = (\WideOr9~0_combout  & ((\u_add_32bit|add [1] & (\u_add_32bit|add[0]~33  & VCC)) # (!\u_add_32bit|add [1] & (!\u_add_32bit|add[0]~33 )))) # (!\WideOr9~0_combout  & ((\u_add_32bit|add [1] & (!\u_add_32bit|add[0]~33 )) # 
// (!\u_add_32bit|add [1] & ((\u_add_32bit|add[0]~33 ) # (GND)))))
// \u_add_32bit|add[1]~35  = CARRY((\WideOr9~0_combout  & (!\u_add_32bit|add [1] & !\u_add_32bit|add[0]~33 )) # (!\WideOr9~0_combout  & ((!\u_add_32bit|add[0]~33 ) # (!\u_add_32bit|add [1]))))

	.dataa(\WideOr9~0_combout ),
	.datab(\u_add_32bit|add [1]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[0]~33 ),
	.combout(\u_add_32bit|add[1]~34_combout ),
	.cout(\u_add_32bit|add[1]~35 ));
// synopsys translate_off
defparam \u_add_32bit|add[1]~34 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[1]~34 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N3
dffeas \u_add_32bit|add[1] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[1]~34_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [1]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[1] .is_wysiwyg = "true";
defparam \u_add_32bit|add[1] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N4
cycloneive_lcell_comb \u_add_32bit|add[2]~36 (
// Equation(s):
// \u_add_32bit|add[2]~36_combout  = ((\WideOr28~0_combout  $ (\u_add_32bit|add [2] $ (\u_add_32bit|add[1]~35 )))) # (GND)
// \u_add_32bit|add[2]~37  = CARRY((\WideOr28~0_combout  & (\u_add_32bit|add [2] & !\u_add_32bit|add[1]~35 )) # (!\WideOr28~0_combout  & ((\u_add_32bit|add [2]) # (!\u_add_32bit|add[1]~35 ))))

	.dataa(\WideOr28~0_combout ),
	.datab(\u_add_32bit|add [2]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[1]~35 ),
	.combout(\u_add_32bit|add[2]~36_combout ),
	.cout(\u_add_32bit|add[2]~37 ));
// synopsys translate_off
defparam \u_add_32bit|add[2]~36 .lut_mask = 16'h964D;
defparam \u_add_32bit|add[2]~36 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N5
dffeas \u_add_32bit|add[2] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[2]~36_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [2]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[2] .is_wysiwyg = "true";
defparam \u_add_32bit|add[2] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N6
cycloneive_lcell_comb \u_add_32bit|add[3]~38 (
// Equation(s):
// \u_add_32bit|add[3]~38_combout  = (\WideOr23~0_combout  & ((\u_add_32bit|add [3] & (!\u_add_32bit|add[2]~37 )) # (!\u_add_32bit|add [3] & ((\u_add_32bit|add[2]~37 ) # (GND))))) # (!\WideOr23~0_combout  & ((\u_add_32bit|add [3] & (\u_add_32bit|add[2]~37  & 
// VCC)) # (!\u_add_32bit|add [3] & (!\u_add_32bit|add[2]~37 ))))
// \u_add_32bit|add[3]~39  = CARRY((\WideOr23~0_combout  & ((!\u_add_32bit|add[2]~37 ) # (!\u_add_32bit|add [3]))) # (!\WideOr23~0_combout  & (!\u_add_32bit|add [3] & !\u_add_32bit|add[2]~37 )))

	.dataa(\WideOr23~0_combout ),
	.datab(\u_add_32bit|add [3]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[2]~37 ),
	.combout(\u_add_32bit|add[3]~38_combout ),
	.cout(\u_add_32bit|add[3]~39 ));
// synopsys translate_off
defparam \u_add_32bit|add[3]~38 .lut_mask = 16'h692B;
defparam \u_add_32bit|add[3]~38 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N7
dffeas \u_add_32bit|add[3] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[3]~38_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [3]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[3] .is_wysiwyg = "true";
defparam \u_add_32bit|add[3] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N8
cycloneive_lcell_comb \u_add_32bit|add[4]~40 (
// Equation(s):
// \u_add_32bit|add[4]~40_combout  = ((\WideOr26~0_combout  $ (\u_add_32bit|add [4] $ (\u_add_32bit|add[3]~39 )))) # (GND)
// \u_add_32bit|add[4]~41  = CARRY((\WideOr26~0_combout  & (\u_add_32bit|add [4] & !\u_add_32bit|add[3]~39 )) # (!\WideOr26~0_combout  & ((\u_add_32bit|add [4]) # (!\u_add_32bit|add[3]~39 ))))

	.dataa(\WideOr26~0_combout ),
	.datab(\u_add_32bit|add [4]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[3]~39 ),
	.combout(\u_add_32bit|add[4]~40_combout ),
	.cout(\u_add_32bit|add[4]~41 ));
// synopsys translate_off
defparam \u_add_32bit|add[4]~40 .lut_mask = 16'h964D;
defparam \u_add_32bit|add[4]~40 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N9
dffeas \u_add_32bit|add[4] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[4]~40_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [4]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[4] .is_wysiwyg = "true";
defparam \u_add_32bit|add[4] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N10
cycloneive_lcell_comb \u_add_32bit|add[5]~42 (
// Equation(s):
// \u_add_32bit|add[5]~42_combout  = (\WideOr5~0_combout  & ((\u_add_32bit|add [5] & (\u_add_32bit|add[4]~41  & VCC)) # (!\u_add_32bit|add [5] & (!\u_add_32bit|add[4]~41 )))) # (!\WideOr5~0_combout  & ((\u_add_32bit|add [5] & (!\u_add_32bit|add[4]~41 )) # 
// (!\u_add_32bit|add [5] & ((\u_add_32bit|add[4]~41 ) # (GND)))))
// \u_add_32bit|add[5]~43  = CARRY((\WideOr5~0_combout  & (!\u_add_32bit|add [5] & !\u_add_32bit|add[4]~41 )) # (!\WideOr5~0_combout  & ((!\u_add_32bit|add[4]~41 ) # (!\u_add_32bit|add [5]))))

	.dataa(\WideOr5~0_combout ),
	.datab(\u_add_32bit|add [5]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[4]~41 ),
	.combout(\u_add_32bit|add[5]~42_combout ),
	.cout(\u_add_32bit|add[5]~43 ));
// synopsys translate_off
defparam \u_add_32bit|add[5]~42 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[5]~42 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N11
dffeas \u_add_32bit|add[5] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[5]~42_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [5]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[5] .is_wysiwyg = "true";
defparam \u_add_32bit|add[5] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N12
cycloneive_lcell_comb \u_add_32bit|add[6]~44 (
// Equation(s):
// \u_add_32bit|add[6]~44_combout  = ((\u_add_32bit|add [6] $ (\WideOr24~0_combout  $ (\u_add_32bit|add[5]~43 )))) # (GND)
// \u_add_32bit|add[6]~45  = CARRY((\u_add_32bit|add [6] & ((!\u_add_32bit|add[5]~43 ) # (!\WideOr24~0_combout ))) # (!\u_add_32bit|add [6] & (!\WideOr24~0_combout  & !\u_add_32bit|add[5]~43 )))

	.dataa(\u_add_32bit|add [6]),
	.datab(\WideOr24~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[5]~43 ),
	.combout(\u_add_32bit|add[6]~44_combout ),
	.cout(\u_add_32bit|add[6]~45 ));
// synopsys translate_off
defparam \u_add_32bit|add[6]~44 .lut_mask = 16'h962B;
defparam \u_add_32bit|add[6]~44 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N13
dffeas \u_add_32bit|add[6] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[6]~44_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [6]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[6] .is_wysiwyg = "true";
defparam \u_add_32bit|add[6] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N14
cycloneive_lcell_comb \u_add_32bit|add[7]~46 (
// Equation(s):
// \u_add_32bit|add[7]~46_combout  = (\WideOr23~0_combout  & ((\u_add_32bit|add [7] & (!\u_add_32bit|add[6]~45 )) # (!\u_add_32bit|add [7] & ((\u_add_32bit|add[6]~45 ) # (GND))))) # (!\WideOr23~0_combout  & ((\u_add_32bit|add [7] & (\u_add_32bit|add[6]~45  & 
// VCC)) # (!\u_add_32bit|add [7] & (!\u_add_32bit|add[6]~45 ))))
// \u_add_32bit|add[7]~47  = CARRY((\WideOr23~0_combout  & ((!\u_add_32bit|add[6]~45 ) # (!\u_add_32bit|add [7]))) # (!\WideOr23~0_combout  & (!\u_add_32bit|add [7] & !\u_add_32bit|add[6]~45 )))

	.dataa(\WideOr23~0_combout ),
	.datab(\u_add_32bit|add [7]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[6]~45 ),
	.combout(\u_add_32bit|add[7]~46_combout ),
	.cout(\u_add_32bit|add[7]~47 ));
// synopsys translate_off
defparam \u_add_32bit|add[7]~46 .lut_mask = 16'h692B;
defparam \u_add_32bit|add[7]~46 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N15
dffeas \u_add_32bit|add[7] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[7]~46_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [7]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[7] .is_wysiwyg = "true";
defparam \u_add_32bit|add[7] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N16
cycloneive_lcell_comb \u_add_32bit|add[8]~48 (
// Equation(s):
// \u_add_32bit|add[8]~48_combout  = ((\u_add_32bit|add [8] $ (\WideOr2~0_combout  $ (!\u_add_32bit|add[7]~47 )))) # (GND)
// \u_add_32bit|add[8]~49  = CARRY((\u_add_32bit|add [8] & ((\WideOr2~0_combout ) # (!\u_add_32bit|add[7]~47 ))) # (!\u_add_32bit|add [8] & (\WideOr2~0_combout  & !\u_add_32bit|add[7]~47 )))

	.dataa(\u_add_32bit|add [8]),
	.datab(\WideOr2~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[7]~47 ),
	.combout(\u_add_32bit|add[8]~48_combout ),
	.cout(\u_add_32bit|add[8]~49 ));
// synopsys translate_off
defparam \u_add_32bit|add[8]~48 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[8]~48 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N17
dffeas \u_add_32bit|add[8] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[8]~48_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [8]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[8] .is_wysiwyg = "true";
defparam \u_add_32bit|add[8] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N18
cycloneive_lcell_comb \u_add_32bit|add[9]~50 (
// Equation(s):
// \u_add_32bit|add[9]~50_combout  = (\u_add_32bit|add [9] & ((\WideOr13~0_combout  & (!\u_add_32bit|add[8]~49 )) # (!\WideOr13~0_combout  & (\u_add_32bit|add[8]~49  & VCC)))) # (!\u_add_32bit|add [9] & ((\WideOr13~0_combout  & ((\u_add_32bit|add[8]~49 ) # 
// (GND))) # (!\WideOr13~0_combout  & (!\u_add_32bit|add[8]~49 ))))
// \u_add_32bit|add[9]~51  = CARRY((\u_add_32bit|add [9] & (\WideOr13~0_combout  & !\u_add_32bit|add[8]~49 )) # (!\u_add_32bit|add [9] & ((\WideOr13~0_combout ) # (!\u_add_32bit|add[8]~49 ))))

	.dataa(\u_add_32bit|add [9]),
	.datab(\WideOr13~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[8]~49 ),
	.combout(\u_add_32bit|add[9]~50_combout ),
	.cout(\u_add_32bit|add[9]~51 ));
// synopsys translate_off
defparam \u_add_32bit|add[9]~50 .lut_mask = 16'h694D;
defparam \u_add_32bit|add[9]~50 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N19
dffeas \u_add_32bit|add[9] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[9]~50_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [9]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[9] .is_wysiwyg = "true";
defparam \u_add_32bit|add[9] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N20
cycloneive_lcell_comb \u_add_32bit|add[10]~52 (
// Equation(s):
// \u_add_32bit|add[10]~52_combout  = ((\WideOr20~0_combout  $ (\u_add_32bit|add [10] $ (\u_add_32bit|add[9]~51 )))) # (GND)
// \u_add_32bit|add[10]~53  = CARRY((\WideOr20~0_combout  & (\u_add_32bit|add [10] & !\u_add_32bit|add[9]~51 )) # (!\WideOr20~0_combout  & ((\u_add_32bit|add [10]) # (!\u_add_32bit|add[9]~51 ))))

	.dataa(\WideOr20~0_combout ),
	.datab(\u_add_32bit|add [10]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[9]~51 ),
	.combout(\u_add_32bit|add[10]~52_combout ),
	.cout(\u_add_32bit|add[10]~53 ));
// synopsys translate_off
defparam \u_add_32bit|add[10]~52 .lut_mask = 16'h964D;
defparam \u_add_32bit|add[10]~52 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N21
dffeas \u_add_32bit|add[10] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[10]~52_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [10]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[10] .is_wysiwyg = "true";
defparam \u_add_32bit|add[10] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N22
cycloneive_lcell_comb \u_add_32bit|add[11]~54 (
// Equation(s):
// \u_add_32bit|add[11]~54_combout  = (\u_add_32bit|add [11] & ((\WideOr19~0_combout  & (\u_add_32bit|add[10]~53  & VCC)) # (!\WideOr19~0_combout  & (!\u_add_32bit|add[10]~53 )))) # (!\u_add_32bit|add [11] & ((\WideOr19~0_combout  & (!\u_add_32bit|add[10]~53 
// )) # (!\WideOr19~0_combout  & ((\u_add_32bit|add[10]~53 ) # (GND)))))
// \u_add_32bit|add[11]~55  = CARRY((\u_add_32bit|add [11] & (!\WideOr19~0_combout  & !\u_add_32bit|add[10]~53 )) # (!\u_add_32bit|add [11] & ((!\u_add_32bit|add[10]~53 ) # (!\WideOr19~0_combout ))))

	.dataa(\u_add_32bit|add [11]),
	.datab(\WideOr19~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[10]~53 ),
	.combout(\u_add_32bit|add[11]~54_combout ),
	.cout(\u_add_32bit|add[11]~55 ));
// synopsys translate_off
defparam \u_add_32bit|add[11]~54 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[11]~54 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N23
dffeas \u_add_32bit|add[11] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[11]~54_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [11]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[11] .is_wysiwyg = "true";
defparam \u_add_32bit|add[11] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N24
cycloneive_lcell_comb \u_add_32bit|add[12]~56 (
// Equation(s):
// \u_add_32bit|add[12]~56_combout  = ((\u_add_32bit|add [12] $ (\WideOr2~0_combout  $ (!\u_add_32bit|add[11]~55 )))) # (GND)
// \u_add_32bit|add[12]~57  = CARRY((\u_add_32bit|add [12] & ((\WideOr2~0_combout ) # (!\u_add_32bit|add[11]~55 ))) # (!\u_add_32bit|add [12] & (\WideOr2~0_combout  & !\u_add_32bit|add[11]~55 )))

	.dataa(\u_add_32bit|add [12]),
	.datab(\WideOr2~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[11]~55 ),
	.combout(\u_add_32bit|add[12]~56_combout ),
	.cout(\u_add_32bit|add[12]~57 ));
// synopsys translate_off
defparam \u_add_32bit|add[12]~56 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[12]~56 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N25
dffeas \u_add_32bit|add[12] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[12]~56_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [12]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[12] .is_wysiwyg = "true";
defparam \u_add_32bit|add[12] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N26
cycloneive_lcell_comb \u_add_32bit|add[13]~58 (
// Equation(s):
// \u_add_32bit|add[13]~58_combout  = (\u_add_32bit|add [13] & ((\WideOr17~0_combout  & (\u_add_32bit|add[12]~57  & VCC)) # (!\WideOr17~0_combout  & (!\u_add_32bit|add[12]~57 )))) # (!\u_add_32bit|add [13] & ((\WideOr17~0_combout  & (!\u_add_32bit|add[12]~57 
// )) # (!\WideOr17~0_combout  & ((\u_add_32bit|add[12]~57 ) # (GND)))))
// \u_add_32bit|add[13]~59  = CARRY((\u_add_32bit|add [13] & (!\WideOr17~0_combout  & !\u_add_32bit|add[12]~57 )) # (!\u_add_32bit|add [13] & ((!\u_add_32bit|add[12]~57 ) # (!\WideOr17~0_combout ))))

	.dataa(\u_add_32bit|add [13]),
	.datab(\WideOr17~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[12]~57 ),
	.combout(\u_add_32bit|add[13]~58_combout ),
	.cout(\u_add_32bit|add[13]~59 ));
// synopsys translate_off
defparam \u_add_32bit|add[13]~58 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[13]~58 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N27
dffeas \u_add_32bit|add[13] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[13]~58_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [13]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[13] .is_wysiwyg = "true";
defparam \u_add_32bit|add[13] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N28
cycloneive_lcell_comb \u_add_32bit|add[14]~60 (
// Equation(s):
// \u_add_32bit|add[14]~60_combout  = ((\WideOr16~0_combout  $ (\u_add_32bit|add [14] $ (\u_add_32bit|add[13]~59 )))) # (GND)
// \u_add_32bit|add[14]~61  = CARRY((\WideOr16~0_combout  & (\u_add_32bit|add [14] & !\u_add_32bit|add[13]~59 )) # (!\WideOr16~0_combout  & ((\u_add_32bit|add [14]) # (!\u_add_32bit|add[13]~59 ))))

	.dataa(\WideOr16~0_combout ),
	.datab(\u_add_32bit|add [14]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[13]~59 ),
	.combout(\u_add_32bit|add[14]~60_combout ),
	.cout(\u_add_32bit|add[14]~61 ));
// synopsys translate_off
defparam \u_add_32bit|add[14]~60 .lut_mask = 16'h964D;
defparam \u_add_32bit|add[14]~60 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N29
dffeas \u_add_32bit|add[14] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[14]~60_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [14]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[14] .is_wysiwyg = "true";
defparam \u_add_32bit|add[14] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y11_N30
cycloneive_lcell_comb \u_add_32bit|add[15]~62 (
// Equation(s):
// \u_add_32bit|add[15]~62_combout  = (\u_add_32bit|add [15] & ((\WideOr15~0_combout  & (\u_add_32bit|add[14]~61  & VCC)) # (!\WideOr15~0_combout  & (!\u_add_32bit|add[14]~61 )))) # (!\u_add_32bit|add [15] & ((\WideOr15~0_combout  & (!\u_add_32bit|add[14]~61 
// )) # (!\WideOr15~0_combout  & ((\u_add_32bit|add[14]~61 ) # (GND)))))
// \u_add_32bit|add[15]~63  = CARRY((\u_add_32bit|add [15] & (!\WideOr15~0_combout  & !\u_add_32bit|add[14]~61 )) # (!\u_add_32bit|add [15] & ((!\u_add_32bit|add[14]~61 ) # (!\WideOr15~0_combout ))))

	.dataa(\u_add_32bit|add [15]),
	.datab(\WideOr15~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[14]~61 ),
	.combout(\u_add_32bit|add[15]~62_combout ),
	.cout(\u_add_32bit|add[15]~63 ));
// synopsys translate_off
defparam \u_add_32bit|add[15]~62 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[15]~62 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y11_N31
dffeas \u_add_32bit|add[15] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[15]~62_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [15]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[15] .is_wysiwyg = "true";
defparam \u_add_32bit|add[15] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N0
cycloneive_lcell_comb \u_add_32bit|add[16]~64 (
// Equation(s):
// \u_add_32bit|add[16]~64_combout  = ((\u_add_32bit|add [16] $ (\WideOr14~0_combout  $ (\u_add_32bit|add[15]~63 )))) # (GND)
// \u_add_32bit|add[16]~65  = CARRY((\u_add_32bit|add [16] & ((!\u_add_32bit|add[15]~63 ) # (!\WideOr14~0_combout ))) # (!\u_add_32bit|add [16] & (!\WideOr14~0_combout  & !\u_add_32bit|add[15]~63 )))

	.dataa(\u_add_32bit|add [16]),
	.datab(\WideOr14~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[15]~63 ),
	.combout(\u_add_32bit|add[16]~64_combout ),
	.cout(\u_add_32bit|add[16]~65 ));
// synopsys translate_off
defparam \u_add_32bit|add[16]~64 .lut_mask = 16'h962B;
defparam \u_add_32bit|add[16]~64 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N1
dffeas \u_add_32bit|add[16] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[16]~64_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [16]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[16] .is_wysiwyg = "true";
defparam \u_add_32bit|add[16] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N2
cycloneive_lcell_comb \u_add_32bit|add[17]~66 (
// Equation(s):
// \u_add_32bit|add[17]~66_combout  = (\WideOr13~0_combout  & ((\u_add_32bit|add [17] & (!\u_add_32bit|add[16]~65 )) # (!\u_add_32bit|add [17] & ((\u_add_32bit|add[16]~65 ) # (GND))))) # (!\WideOr13~0_combout  & ((\u_add_32bit|add [17] & 
// (\u_add_32bit|add[16]~65  & VCC)) # (!\u_add_32bit|add [17] & (!\u_add_32bit|add[16]~65 ))))
// \u_add_32bit|add[17]~67  = CARRY((\WideOr13~0_combout  & ((!\u_add_32bit|add[16]~65 ) # (!\u_add_32bit|add [17]))) # (!\WideOr13~0_combout  & (!\u_add_32bit|add [17] & !\u_add_32bit|add[16]~65 )))

	.dataa(\WideOr13~0_combout ),
	.datab(\u_add_32bit|add [17]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[16]~65 ),
	.combout(\u_add_32bit|add[17]~66_combout ),
	.cout(\u_add_32bit|add[17]~67 ));
// synopsys translate_off
defparam \u_add_32bit|add[17]~66 .lut_mask = 16'h692B;
defparam \u_add_32bit|add[17]~66 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N3
dffeas \u_add_32bit|add[17] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[17]~66_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [17]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[17] .is_wysiwyg = "true";
defparam \u_add_32bit|add[17] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N4
cycloneive_lcell_comb \u_add_32bit|add[18]~68 (
// Equation(s):
// \u_add_32bit|add[18]~68_combout  = ((\WideOr8~0_combout  $ (\u_add_32bit|add [18] $ (!\u_add_32bit|add[17]~67 )))) # (GND)
// \u_add_32bit|add[18]~69  = CARRY((\WideOr8~0_combout  & ((\u_add_32bit|add [18]) # (!\u_add_32bit|add[17]~67 ))) # (!\WideOr8~0_combout  & (\u_add_32bit|add [18] & !\u_add_32bit|add[17]~67 )))

	.dataa(\WideOr8~0_combout ),
	.datab(\u_add_32bit|add [18]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[17]~67 ),
	.combout(\u_add_32bit|add[18]~68_combout ),
	.cout(\u_add_32bit|add[18]~69 ));
// synopsys translate_off
defparam \u_add_32bit|add[18]~68 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[18]~68 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N5
dffeas \u_add_32bit|add[18] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[18]~68_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [18]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[18] .is_wysiwyg = "true";
defparam \u_add_32bit|add[18] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N6
cycloneive_lcell_comb \u_add_32bit|add[19]~70 (
// Equation(s):
// \u_add_32bit|add[19]~70_combout  = (\u_add_32bit|add [19] & ((\WideOr3~0_combout  & (\u_add_32bit|add[18]~69  & VCC)) # (!\WideOr3~0_combout  & (!\u_add_32bit|add[18]~69 )))) # (!\u_add_32bit|add [19] & ((\WideOr3~0_combout  & (!\u_add_32bit|add[18]~69 )) 
// # (!\WideOr3~0_combout  & ((\u_add_32bit|add[18]~69 ) # (GND)))))
// \u_add_32bit|add[19]~71  = CARRY((\u_add_32bit|add [19] & (!\WideOr3~0_combout  & !\u_add_32bit|add[18]~69 )) # (!\u_add_32bit|add [19] & ((!\u_add_32bit|add[18]~69 ) # (!\WideOr3~0_combout ))))

	.dataa(\u_add_32bit|add [19]),
	.datab(\WideOr3~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[18]~69 ),
	.combout(\u_add_32bit|add[19]~70_combout ),
	.cout(\u_add_32bit|add[19]~71 ));
// synopsys translate_off
defparam \u_add_32bit|add[19]~70 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[19]~70 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N7
dffeas \u_add_32bit|add[19] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[19]~70_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [19]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[19] .is_wysiwyg = "true";
defparam \u_add_32bit|add[19] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N8
cycloneive_lcell_comb \u_add_32bit|add[20]~72 (
// Equation(s):
// \u_add_32bit|add[20]~72_combout  = ((\u_add_32bit|add [20] $ (\WideOr2~0_combout  $ (!\u_add_32bit|add[19]~71 )))) # (GND)
// \u_add_32bit|add[20]~73  = CARRY((\u_add_32bit|add [20] & ((\WideOr2~0_combout ) # (!\u_add_32bit|add[19]~71 ))) # (!\u_add_32bit|add [20] & (\WideOr2~0_combout  & !\u_add_32bit|add[19]~71 )))

	.dataa(\u_add_32bit|add [20]),
	.datab(\WideOr2~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[19]~71 ),
	.combout(\u_add_32bit|add[20]~72_combout ),
	.cout(\u_add_32bit|add[20]~73 ));
// synopsys translate_off
defparam \u_add_32bit|add[20]~72 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[20]~72 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N9
dffeas \u_add_32bit|add[20] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[20]~72_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [20]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[20] .is_wysiwyg = "true";
defparam \u_add_32bit|add[20] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N10
cycloneive_lcell_comb \u_add_32bit|add[21]~74 (
// Equation(s):
// \u_add_32bit|add[21]~74_combout  = (\u_add_32bit|add [21] & ((\WideOr9~0_combout  & (\u_add_32bit|add[20]~73  & VCC)) # (!\WideOr9~0_combout  & (!\u_add_32bit|add[20]~73 )))) # (!\u_add_32bit|add [21] & ((\WideOr9~0_combout  & (!\u_add_32bit|add[20]~73 )) 
// # (!\WideOr9~0_combout  & ((\u_add_32bit|add[20]~73 ) # (GND)))))
// \u_add_32bit|add[21]~75  = CARRY((\u_add_32bit|add [21] & (!\WideOr9~0_combout  & !\u_add_32bit|add[20]~73 )) # (!\u_add_32bit|add [21] & ((!\u_add_32bit|add[20]~73 ) # (!\WideOr9~0_combout ))))

	.dataa(\u_add_32bit|add [21]),
	.datab(\WideOr9~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[20]~73 ),
	.combout(\u_add_32bit|add[21]~74_combout ),
	.cout(\u_add_32bit|add[21]~75 ));
// synopsys translate_off
defparam \u_add_32bit|add[21]~74 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[21]~74 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N11
dffeas \u_add_32bit|add[21] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[21]~74_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [21]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[21] .is_wysiwyg = "true";
defparam \u_add_32bit|add[21] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N12
cycloneive_lcell_comb \u_add_32bit|add[22]~76 (
// Equation(s):
// \u_add_32bit|add[22]~76_combout  = ((\WideOr8~0_combout  $ (\u_add_32bit|add [22] $ (!\u_add_32bit|add[21]~75 )))) # (GND)
// \u_add_32bit|add[22]~77  = CARRY((\WideOr8~0_combout  & ((\u_add_32bit|add [22]) # (!\u_add_32bit|add[21]~75 ))) # (!\WideOr8~0_combout  & (\u_add_32bit|add [22] & !\u_add_32bit|add[21]~75 )))

	.dataa(\WideOr8~0_combout ),
	.datab(\u_add_32bit|add [22]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[21]~75 ),
	.combout(\u_add_32bit|add[22]~76_combout ),
	.cout(\u_add_32bit|add[22]~77 ));
// synopsys translate_off
defparam \u_add_32bit|add[22]~76 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[22]~76 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N13
dffeas \u_add_32bit|add[22] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[22]~76_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [22]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[22] .is_wysiwyg = "true";
defparam \u_add_32bit|add[22] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N14
cycloneive_lcell_comb \u_add_32bit|add[23]~78 (
// Equation(s):
// \u_add_32bit|add[23]~78_combout  = (\u_add_32bit|add [23] & ((\WideOr3~0_combout  & (\u_add_32bit|add[22]~77  & VCC)) # (!\WideOr3~0_combout  & (!\u_add_32bit|add[22]~77 )))) # (!\u_add_32bit|add [23] & ((\WideOr3~0_combout  & (!\u_add_32bit|add[22]~77 )) 
// # (!\WideOr3~0_combout  & ((\u_add_32bit|add[22]~77 ) # (GND)))))
// \u_add_32bit|add[23]~79  = CARRY((\u_add_32bit|add [23] & (!\WideOr3~0_combout  & !\u_add_32bit|add[22]~77 )) # (!\u_add_32bit|add [23] & ((!\u_add_32bit|add[22]~77 ) # (!\WideOr3~0_combout ))))

	.dataa(\u_add_32bit|add [23]),
	.datab(\WideOr3~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[22]~77 ),
	.combout(\u_add_32bit|add[23]~78_combout ),
	.cout(\u_add_32bit|add[23]~79 ));
// synopsys translate_off
defparam \u_add_32bit|add[23]~78 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[23]~78 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N15
dffeas \u_add_32bit|add[23] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[23]~78_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [23]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[23] .is_wysiwyg = "true";
defparam \u_add_32bit|add[23] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N4
cycloneive_lcell_comb \WideOr6~0 (
// Equation(s):
// \WideOr6~0_combout  = (spi_data[14] & (spi_data[13] $ (((!spi_data[15]))))) # (!spi_data[14] & ((spi_data[13] & (spi_data[12])) # (!spi_data[13] & (!spi_data[12] & spi_data[15]))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr6~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr6~0 .lut_mask = 16'hC962;
defparam \WideOr6~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N16
cycloneive_lcell_comb \u_add_32bit|add[24]~80 (
// Equation(s):
// \u_add_32bit|add[24]~80_combout  = ((\u_add_32bit|add [24] $ (\WideOr6~0_combout  $ (!\u_add_32bit|add[23]~79 )))) # (GND)
// \u_add_32bit|add[24]~81  = CARRY((\u_add_32bit|add [24] & ((\WideOr6~0_combout ) # (!\u_add_32bit|add[23]~79 ))) # (!\u_add_32bit|add [24] & (\WideOr6~0_combout  & !\u_add_32bit|add[23]~79 )))

	.dataa(\u_add_32bit|add [24]),
	.datab(\WideOr6~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[23]~79 ),
	.combout(\u_add_32bit|add[24]~80_combout ),
	.cout(\u_add_32bit|add[24]~81 ));
// synopsys translate_off
defparam \u_add_32bit|add[24]~80 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[24]~80 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N17
dffeas \u_add_32bit|add[24] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[24]~80_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [24]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[24] .is_wysiwyg = "true";
defparam \u_add_32bit|add[24] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N18
cycloneive_lcell_comb \u_add_32bit|add[25]~82 (
// Equation(s):
// \u_add_32bit|add[25]~82_combout  = (\u_add_32bit|add [25] & ((\WideOr5~0_combout  & (\u_add_32bit|add[24]~81  & VCC)) # (!\WideOr5~0_combout  & (!\u_add_32bit|add[24]~81 )))) # (!\u_add_32bit|add [25] & ((\WideOr5~0_combout  & (!\u_add_32bit|add[24]~81 )) 
// # (!\WideOr5~0_combout  & ((\u_add_32bit|add[24]~81 ) # (GND)))))
// \u_add_32bit|add[25]~83  = CARRY((\u_add_32bit|add [25] & (!\WideOr5~0_combout  & !\u_add_32bit|add[24]~81 )) # (!\u_add_32bit|add [25] & ((!\u_add_32bit|add[24]~81 ) # (!\WideOr5~0_combout ))))

	.dataa(\u_add_32bit|add [25]),
	.datab(\WideOr5~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[24]~81 ),
	.combout(\u_add_32bit|add[25]~82_combout ),
	.cout(\u_add_32bit|add[25]~83 ));
// synopsys translate_off
defparam \u_add_32bit|add[25]~82 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[25]~82 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N19
dffeas \u_add_32bit|add[25] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[25]~82_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [25]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[25] .is_wysiwyg = "true";
defparam \u_add_32bit|add[25] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N22
cycloneive_lcell_comb \WideOr4~0 (
// Equation(s):
// \WideOr4~0_combout  = (spi_data[13] & (((!spi_data[12] & !spi_data[15])))) # (!spi_data[13] & (spi_data[15] & ((spi_data[14]) # (spi_data[12]))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr4~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr4~0 .lut_mask = 16'h320C;
defparam \WideOr4~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N20
cycloneive_lcell_comb \u_add_32bit|add[26]~84 (
// Equation(s):
// \u_add_32bit|add[26]~84_combout  = ((\u_add_32bit|add [26] $ (\WideOr4~0_combout  $ (!\u_add_32bit|add[25]~83 )))) # (GND)
// \u_add_32bit|add[26]~85  = CARRY((\u_add_32bit|add [26] & ((\WideOr4~0_combout ) # (!\u_add_32bit|add[25]~83 ))) # (!\u_add_32bit|add [26] & (\WideOr4~0_combout  & !\u_add_32bit|add[25]~83 )))

	.dataa(\u_add_32bit|add [26]),
	.datab(\WideOr4~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[25]~83 ),
	.combout(\u_add_32bit|add[26]~84_combout ),
	.cout(\u_add_32bit|add[26]~85 ));
// synopsys translate_off
defparam \u_add_32bit|add[26]~84 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[26]~84 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N21
dffeas \u_add_32bit|add[26] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[26]~84_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [26]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[26] .is_wysiwyg = "true";
defparam \u_add_32bit|add[26] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N22
cycloneive_lcell_comb \u_add_32bit|add[27]~86 (
// Equation(s):
// \u_add_32bit|add[27]~86_combout  = (\u_add_32bit|add [27] & ((\WideOr3~0_combout  & (\u_add_32bit|add[26]~85  & VCC)) # (!\WideOr3~0_combout  & (!\u_add_32bit|add[26]~85 )))) # (!\u_add_32bit|add [27] & ((\WideOr3~0_combout  & (!\u_add_32bit|add[26]~85 )) 
// # (!\WideOr3~0_combout  & ((\u_add_32bit|add[26]~85 ) # (GND)))))
// \u_add_32bit|add[27]~87  = CARRY((\u_add_32bit|add [27] & (!\WideOr3~0_combout  & !\u_add_32bit|add[26]~85 )) # (!\u_add_32bit|add [27] & ((!\u_add_32bit|add[26]~85 ) # (!\WideOr3~0_combout ))))

	.dataa(\u_add_32bit|add [27]),
	.datab(\WideOr3~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[26]~85 ),
	.combout(\u_add_32bit|add[27]~86_combout ),
	.cout(\u_add_32bit|add[27]~87 ));
// synopsys translate_off
defparam \u_add_32bit|add[27]~86 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[27]~86 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N23
dffeas \u_add_32bit|add[27] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[27]~86_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [27]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[27] .is_wysiwyg = "true";
defparam \u_add_32bit|add[27] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N24
cycloneive_lcell_comb \u_add_32bit|add[28]~88 (
// Equation(s):
// \u_add_32bit|add[28]~88_combout  = ((\u_add_32bit|add [28] $ (\WideOr2~0_combout  $ (!\u_add_32bit|add[27]~87 )))) # (GND)
// \u_add_32bit|add[28]~89  = CARRY((\u_add_32bit|add [28] & ((\WideOr2~0_combout ) # (!\u_add_32bit|add[27]~87 ))) # (!\u_add_32bit|add [28] & (\WideOr2~0_combout  & !\u_add_32bit|add[27]~87 )))

	.dataa(\u_add_32bit|add [28]),
	.datab(\WideOr2~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[27]~87 ),
	.combout(\u_add_32bit|add[28]~88_combout ),
	.cout(\u_add_32bit|add[28]~89 ));
// synopsys translate_off
defparam \u_add_32bit|add[28]~88 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[28]~88 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N25
dffeas \u_add_32bit|add[28] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[28]~88_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [28]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[28] .is_wysiwyg = "true";
defparam \u_add_32bit|add[28] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N24
cycloneive_lcell_comb \WideOr1~0 (
// Equation(s):
// \WideOr1~0_combout  = (spi_data[14] & (spi_data[13] & ((!spi_data[15])))) # (!spi_data[14] & (spi_data[15] & ((spi_data[12]) # (!spi_data[13]))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr1~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr1~0 .lut_mask = 16'h5188;
defparam \WideOr1~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N26
cycloneive_lcell_comb \u_add_32bit|add[29]~90 (
// Equation(s):
// \u_add_32bit|add[29]~90_combout  = (\u_add_32bit|add [29] & ((\WideOr1~0_combout  & (\u_add_32bit|add[28]~89  & VCC)) # (!\WideOr1~0_combout  & (!\u_add_32bit|add[28]~89 )))) # (!\u_add_32bit|add [29] & ((\WideOr1~0_combout  & (!\u_add_32bit|add[28]~89 )) 
// # (!\WideOr1~0_combout  & ((\u_add_32bit|add[28]~89 ) # (GND)))))
// \u_add_32bit|add[29]~91  = CARRY((\u_add_32bit|add [29] & (!\WideOr1~0_combout  & !\u_add_32bit|add[28]~89 )) # (!\u_add_32bit|add [29] & ((!\u_add_32bit|add[28]~89 ) # (!\WideOr1~0_combout ))))

	.dataa(\u_add_32bit|add [29]),
	.datab(\WideOr1~0_combout ),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[28]~89 ),
	.combout(\u_add_32bit|add[29]~90_combout ),
	.cout(\u_add_32bit|add[29]~91 ));
// synopsys translate_off
defparam \u_add_32bit|add[29]~90 .lut_mask = 16'h9617;
defparam \u_add_32bit|add[29]~90 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N27
dffeas \u_add_32bit|add[29] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[29]~90_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [29]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[29] .is_wysiwyg = "true";
defparam \u_add_32bit|add[29] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y12_N18
cycloneive_lcell_comb \WideOr0~0 (
// Equation(s):
// \WideOr0~0_combout  = (spi_data[15] & ((spi_data[14]) # ((spi_data[13] & !spi_data[12]))))

	.dataa(spi_data[14]),
	.datab(spi_data[13]),
	.datac(spi_data[12]),
	.datad(spi_data[15]),
	.cin(gnd),
	.combout(\WideOr0~0_combout ),
	.cout());
// synopsys translate_off
defparam \WideOr0~0 .lut_mask = 16'hAE00;
defparam \WideOr0~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N28
cycloneive_lcell_comb \u_add_32bit|add[30]~92 (
// Equation(s):
// \u_add_32bit|add[30]~92_combout  = ((\WideOr0~0_combout  $ (\u_add_32bit|add [30] $ (!\u_add_32bit|add[29]~91 )))) # (GND)
// \u_add_32bit|add[30]~93  = CARRY((\WideOr0~0_combout  & ((\u_add_32bit|add [30]) # (!\u_add_32bit|add[29]~91 ))) # (!\WideOr0~0_combout  & (\u_add_32bit|add [30] & !\u_add_32bit|add[29]~91 )))

	.dataa(\WideOr0~0_combout ),
	.datab(\u_add_32bit|add [30]),
	.datac(gnd),
	.datad(vcc),
	.cin(\u_add_32bit|add[29]~91 ),
	.combout(\u_add_32bit|add[30]~92_combout ),
	.cout(\u_add_32bit|add[30]~93 ));
// synopsys translate_off
defparam \u_add_32bit|add[30]~92 .lut_mask = 16'h698E;
defparam \u_add_32bit|add[30]~92 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N29
dffeas \u_add_32bit|add[30] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[30]~92_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [30]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[30] .is_wysiwyg = "true";
defparam \u_add_32bit|add[30] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y10_N30
cycloneive_lcell_comb \u_add_32bit|add[31]~94 (
// Equation(s):
// \u_add_32bit|add[31]~94_combout  = \u_add_32bit|add [31] $ (\u_add_32bit|add[30]~93 )

	.dataa(\u_add_32bit|add [31]),
	.datab(gnd),
	.datac(gnd),
	.datad(gnd),
	.cin(\u_add_32bit|add[30]~93 ),
	.combout(\u_add_32bit|add[31]~94_combout ),
	.cout());
// synopsys translate_off
defparam \u_add_32bit|add[31]~94 .lut_mask = 16'h5A5A;
defparam \u_add_32bit|add[31]~94 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: FF_X16_Y10_N31
dffeas \u_add_32bit|add[31] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_add_32bit|add[31]~94_combout ),
	.asdata(vcc),
	.clrn(vcc),
	.aload(gnd),
	.sclr(!\SYS_RST~input_o ),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_add_32bit|add [31]),
	.prn(vcc));
// synopsys translate_off
defparam \u_add_32bit|add[31] .is_wysiwyg = "true";
defparam \u_add_32bit|add[31] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y8_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_first_bit_number = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .mem_init3 = 2048'hE943A50E943E50FA43E943A50FA50FA50FA50FA50FA943EA50FE950FEA503FA9500FEA9500FFEAA554003FFFFAAAAAAAAAAAAAAAAAAFFFC00155AAFF015ABF016AF05ABC1AF05BC5AC1BC5B16C1B1AC6C6C6C6C6C6CB1B6C71861861872CB61D8721D8B7221DD88888B77888889DD227789E278D278D34924D38E4939393939394E53A43A43E50FA940FEA9540003FFFF0000556AFC05AF05AF16F16C5B1B1B1B1B186CB1C72CB61D8762DD8887777777888DD2378D278D24934E39393939390E43A43E50FE9503FEAAA5555AAAAFF015AF05AF16F1BC6C5B186C6DB2CB2CB61D87621DD8888888DDD23789E349E79E7938E4E4E4393E43A50FA50FEA9550000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .mem_init2 = 2048'h000155AAFC16BC16B06F1B06C6C6CB1B6DB6D872D8B721DDC8888889DD22749D278E38E39E4E49394E4F93E53E943E9503FEAAA95556AAAFF015AFC16F06B06C1B1B1B1B1B2C71861CB61CB721DC88B7777774889DE2749D278E34D38E4939393939394E53E53E943E940FEA5540003FFFF000055AAFC05ABC16F06B06B16C5B1B1B1B1B186CB1C61871CB61CB62D8B7621DD88888B77888889DD223789D2349D278E34924924934E7938E4E4E4E4E4E4E9390E5394F90E94F943E90FA943EA503FA9503FEA955000FFFEAAAAAAAAAAAAAAAAABFFFF000556AAFFC015AAFC015ABF016AFC15AFC16AF05ABC16BC16BC16BC16BC16B05AF06BC16F05AC16B05AF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .mem_init1 = 2048'h16BC5AF16BC1AF05BC16BC5AF05AF05AF05AF05AF056BC15AF016AF015AFC056AFF0156AFF00155AABFFC00005555555555555555550003FFEAA5500FEA540FE950FA543E50FA43A53E43A4E93E4E539393939393934E4938E79E79E78D349E278DE2748DDE227777748877777622DD88761D872D872CB6DB2C71B6C6C6C6C6C6B1AC5BC5BC1AF056BF0156ABFFFC0000FFFFAA9503FA50FA50E90E93A4E4E4E4E4E7934E38D349E2789D22777888888877722DC872D872DB6CB1C6C6C6C6C6F1BC5BC1AF016AFC01555AAAA555500FEA50FA50E90E4393A4E793924D34D349E2789DE227777777222DC8761CB6186186C71B1B1BC6C1BC5AF05AF0156AAFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a0 .mem_init0 = 2048'hFFFEAA5503E943E94F90E4F9393934E49249278D2748DE223777777622DD8B62D871C71C61B1B6C6B1B06C1AC16BC16AFC015556AAA955500FEA503E90F94F93E4E4E4E4E4D38E79E349E348DE23774888888B77621D8B62D871CB2C71B6C6C6C6C6C6B1AC1AC16BC16BF015AABFFFC0000FFFFAA5503FA543E90F94F94E93A4E4E4E4E4E7934E39E78E349E349D27489DE227777748877777622DDC8762DCB62D871CB6DB6DB6CB186C71B1B1B1B1B1B16C6F1AC6B06F16B06BC16F056BC15AFC056AFC0156AAFFF00015555555555555555540000FFFAA955003FEA5503FEA540FE9503EA503E950FA543E943E943E943E943E94FA50F943E90FA53E94FA50;
// synopsys translate_on

// Location: LCCOMB_X16_Y6_N24
cycloneive_lcell_comb \u_sel_wave|da_out_reg[0]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[0]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [0]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [0]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[0]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[0]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[0]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X16_Y6_N25
dffeas \u_sel_wave|da_out_reg[0] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[0]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [0]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[0] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[0] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X16_Y6_N4
cycloneive_lcell_comb \u_sel_wave|da_out_reg[1]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[1]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [1]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [1]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[1]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[1]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[1]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X16_Y6_N5
dffeas \u_sel_wave|da_out_reg[1] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[1]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [1]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[1] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[1] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y11_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_first_bit_number = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .mem_init3 = 2048'h1B1AC6C1B1AC6C1B1AC6C5B1BC6C6B1B16C6C1B1BC6C6F1B1BC6C6C1B1B1AC6C6C6B1B1B1BC6C6C6C6C6F1B1B1B1B1B1B1B1B1B1B1B1B186C6C6C6C61B1B1B6C6C61B1B2C6CB1B2C6DB186CB186CB186DB2C7186DB2CB1C71C71C71C71C71CB2DB61871CB61872D872CB62D872D8761CB72DCB721C8762DC8762DD8B7621DC8B77221DDC88B77722222DDDDDDDDDC8888DDDDDDDDDE22227777888DDE227788DD227489D23789D2749D2749E348D278D249E34D279E78E38E38E38E7924D38E4938E4D3934E4E4D393939393939393E4E4E9393E4E93A4F93E53A4F94F94F943A53E94FA50FA50FA503E950FEA503FA9540FFAA55403FFEAAA55550000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .mem_init2 = 2048'h00000000015556AAAFFF00556ABFC055ABF016AFC15AF016BC16BC16BC5AF16B05BC5BC5BC6B16F1BC6B1AC6F1B1AC6C6F1B1B1B1B1B1B1B1C6C6C71B1C6CB186CB1C61B6CB2CB2CB2CB6DB61C72D861CB61C872D8761D8761D8B721D887621DC8B7622DDC88B77762222DDDDDDDDDC8888DDDDDDDDDE22223777888DDD2237788DD227789DE2748DE2748D2378DE378D2749E349E278E349E349278D349279E38D34D34D34D34D34D38E39E4934E39E4938E4938E4939E4E3938E4E393924E4E79393924E4E4E4E493939393939393939393939393E4E4E4E4E4F939393A4E4E4E939390E4E4F9393E4E4F9390E4E5393A4E4F9394E4E9390E4E9390E4E9393;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .mem_init1 = 2048'hE4E5393E4E5393E4E5393A4E439394E4E9393E4E439390E4E439393E4E4E53939394E4E4E439393939390E4E4E4E4E4E4E4E4E4E4E4E4E7939393939E4E4E493939E4E4D3934E4D3924E7934E7934E7924D38E7924D34E38E38E38E38E38E34D249E78E349E78D278D349D278D2789E348D2348DE3789D23789D227489DE237488DDE223774888DDDDD222222222377772222222221DDDD8888777221DD887722DD8B762DC8762D8B62D8B61CB72D872DB61CB2D861871C71C71C7186DB2C71B6C71B2C6CB1B1B2C6C6C6C6C6C6C6C1B1B16C6C1B16C5B06C1AC5B06B06B06BC5AC16B05AF05AF05AFC16AF015AFC056ABF0055AABFC001555AAAAFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a2 .mem_init0 = 2048'hFFFFFFFFFEAAA9555000FFAA95403FAA540FE9503EA50FE943E943E943A50E94FA43A43A4394E90E4394E5390E4E539390E4E4E4E4E4E4E4E393938E4E3934E7934E39E4934D34D34D349249E38D279E349E378D2789E2789E2748DE27789DE237489DD2237748889DDDD222222222377772222222221DDDDC888777222DDC887722DD887621D8B721D8B72DC8721C872D8B61CB61D871CB61CB6D872CB6D861C72CB2CB2CB2CB2CB2C71C61B6CB1C61B6C71B6C71B6C61B1C6C71B1C6C6DB1B186C6C6DB1B1B1B1B6C6C6C6C6C6C6C6C6C6C6C6C6C1B1B1B1B1B06C6C6C5B1B1B16C6C6F1B1B06C6C1B1B06C6F1B1AC6C5B1B06C6B1B16C6F1B16C6F1B16C6C;
// synopsys translate_on

// Location: LCCOMB_X19_Y10_N6
cycloneive_lcell_comb \u_sel_wave|da_out_reg[2]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[2]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [2]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [2]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[2]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[2]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[2]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y10_N7
dffeas \u_sel_wave|da_out_reg[2] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[2]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [2]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[2] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[2] .power_up = "low";
// synopsys translate_on

// Location: FF_X19_Y10_N19
dffeas \u_sel_wave|da_out_reg[3] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [3]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [3]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[3] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[3] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y6_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_first_bit_number = 4;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .mem_init3 = 2048'hE4390E5394E53A4E93A4F93E4F90E4394E53A4E93A4F93E4390E53A4E93E4F90E5394E93E4F90E53A4F93E4394E93E4394E93E4394E93E53A4F90E53E4394E90E53E4394F90E93E53A43A4F94F90E90E53E53E53A43A43A43A43A43A43A43A43A43E53E53E90E90F94F943A53E50E94F943A53E94FA43E50F943A50E943E50F943E94FA50F943E943E90FA50FA50FA50FA50FA50FA543E943E950FA503E940FA543EA50FE940FA940FA940FA950FE9503FA540FE9503FA9503FA9503FEA540FFA9500FEA9500FFA95403FEA95403FEAA55003FEAA55400FFEAA955000FFFAAA9554000FFFFAAAA555540000FFFFFEAAAAAA55555555400000000000000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .mem_init2 = 2048'h00000000000000000000555555556AAAAAAFFFFFC000055556AAABFFFC000555AAABFFC00155AAAFFC00556AAFF00156AAFF0055AAFF0055ABFC015AAFC015ABFC056AFF015ABF015ABF015AFC056BF015AFC15ABC05ABC05ABC05AFC16AF056BC05AF016BC15AF05AF056BC16BC16BC16BC16BC16BC1AF05AF05BC16BC5AF05BC16F05AC16B05BC16F06BC5AF16B05BC5AC16F16B05BC5BC1AC1AF16F16F06B06B06B06B06B06B06B06B06B16F16F16C1AC1BC5BC6B06B16F1AC1BC5B06F16C1AC5B06F16C1BC6B16F1AC5B06F1AC5B06F1AC5B06F1BC6B16C1BC6F1AC5B16C1BC6F1AC6B16C1B06F1BC6B1AC6B16C5B06C1BC6F1BC6B1AC6B16C5B16C1B06F;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .mem_init1 = 2048'h1BC6F1AC6B1AC5B16C5B06C1B06F1BC6B1AC5B16C5B06C1BC6F1AC5B16C1B06F1AC6B16C1B06F1AC5B06C1BC6B16C1BC6B16C1BC6B16C1AC5B06F1AC1BC6B16F1AC1BC6B06F16C1AC5BC5B06B06F16F1AC1AC1AC5BC5BC5BC5BC5BC5BC5BC5BC5BC1AC1AC16F16F06B06BC5AC1AF16B06BC5AC16B05BC1AF06BC5AF16BC1AF06BC16B05AF06BC16BC16F05AF05AF05AF05AF05AF05ABC16BC16AF05AFC16BF05ABC15AF016BF056BF056BF056AF016AFC05ABF016AFC056AFC056AFC015ABF0056AFF0156AFF0056ABFC0156ABFC0155AAFFC0155AABFF001556AAFFF0005556AABFFF00005555AAAABFFFF000001555555AAAAAAAABFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a4 .mem_init0 = 2048'hFFFFFFFFFFFFFFFFFFFFAAAAAAAA9555555000003FFFFAAAA955540003FFFAAA5554003FFEAA555003FFAA95500FFEA95500FFAA5500FFAA5403FEA5503FEA5403FA9500FEA540FEA540FEA503FA940FEA503EA543FA543FA543FA503E950FA943FA50FE943EA50FA50FA943E943E943E943E943E943E50FA50FA43E943A50FA43E90FA53E94FA43E90F943A50E94FA43A53E90E94FA43A43E53E50E90E90F94F94F94F94F94F94F94F94F94E90E90E93E53E43A4394F94E90E53E43A4F90E93E53A4F90E93E4394E90E53A4F90E53A4F90E53A4F90E4394E93E4390E53A4E93E4390E5394E93E4F90E4394E5394E93A4F93E4390E4394E5394E93A4E93E4F90;
// synopsys translate_on

// Location: LCCOMB_X21_Y4_N10
cycloneive_lcell_comb \u_sel_wave|da_out_reg[4]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[4]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [4]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [4]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[4]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[4]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[4]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X21_Y4_N11
dffeas \u_sel_wave|da_out_reg[4] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[4]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [4]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[4] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[4] .power_up = "low";
// synopsys translate_on

// Location: FF_X21_Y4_N9
dffeas \u_sel_wave|da_out_reg[5] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(gnd),
	.asdata(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [5]),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(vcc),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [5]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[5] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[5] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y7_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_first_bit_number = 6;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .mem_init3 = 2048'hFFEAA55400FFEAA55400FFEAA555003FFAA955003FFAA955400FFEAA55400FFFAA955003FFAAA55400FFEAA955003FFEAA554003FFAA955400FFFAA955400FFFAA955400FFFAA9554003FFAAA555000FFEAA9554003FFEAA9554003FFEAA9554003FFEAA9555000FFFAAA9554000FFFAAA9554000FFFEAAA5554000FFFEAAA5554000FFFFAAA95554000FFFFAAAA55550000FFFFAAAA955540000FFFFEAAAA555540000FFFFFAAAAA5555500000FFFFFEAAAAA555554000003FFFFFEAAAAAA55555550000000FFFFFFFEAAAAAAA9555555554000000000FFFFFFFFFFFAAAAAAAAAAAAA5555555555555555500000000000000000000000000000000000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .mem_init2 = 2048'h0000000000000000000000000000000000000000155555555555555556AAAAAAAAAAAABFFFFFFFFFFC000000000555555555AAAAAAAAFFFFFFFC00000015555556AAAAAAFFFFFF000000555556AAAAAFFFFFC0000155556AAAABFFFFC000055556AAAAFFFFC00005555AAAABFFFC00015556AAABFFFC0005555AAABFFFC0005556AAAFFFC0005556AAAFFFC000555AAABFFC000555AAABFFC001555AAAFFF000555AAAFFF000555AAAFFF000555AAAFFC001556AABFF000555AABFFC00555AABFFC00555AABFFC00555AABFF000556AAFFF00155AAAFFC00556AABFF00155AABFFC00556AAFFC00555AABFF00155AABFF001556AAFFC00556AAFFC00556AAFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .mem_init1 = 2048'h00155AABFF00155AABFF00155AAAFFC00556AAFFC00556AABFF00155AABFF000556AAFFC00555AABFF001556AAFFC00155AABFFC00556AABFF000556AABFF000556AABFF000556AABFFC00555AAAFFF001556AABFFC001556AABFFC001556AABFFC001556AAAFFF0005556AABFFF0005556AABFFF0001555AAABFFF0001555AAABFFF00005556AAABFFF00005555AAAAFFFF000055556AAABFFFF000015555AAAABFFFF0000055555AAAAAFFFFF00000155555AAAAABFFFFFC000001555555AAAAAAAFFFFFFF0000000155555556AAAAAAAABFFFFFFFFF000000000005555555555555AAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a6 .mem_init0 = 2048'hFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAA9555555555555400000000003FFFFFFFFFAAAAAAAAA5555555500000003FFFFFFEAAAAAA9555555000000FFFFFFAAAAA955555000003FFFFEAAAA95555400003FFFFAAAA9555500003FFFFAAAA555540003FFFEAAA955540003FFFAAAA55540003FFFAAA95550003FFFAAA95550003FFFAAA5554003FFFAAA5554003FFEAAA555000FFFAAA555000FFFAAA555000FFFAAA555003FFEAA955400FFFAAA554003FFAAA554003FFAAA554003FFAAA55400FFFAA955000FFEAA555003FFAA955400FFEAA554003FFAA955003FFAAA55400FFEAA55400FFEAA955003FFAA955003FFAA955000;
// synopsys translate_on

// Location: LCCOMB_X19_Y4_N0
cycloneive_lcell_comb \u_sel_wave|da_out_reg[6]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[6]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [6]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [6]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[6]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[6]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[6]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X19_Y4_N1
dffeas \u_sel_wave|da_out_reg[6] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[6]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [6]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[6] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[6] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X18_Y7_N30
cycloneive_lcell_comb \u_sel_wave|da_out_reg[7]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[7]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [7]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [7]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[7]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[7]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[7]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X18_Y7_N31
dffeas \u_sel_wave|da_out_reg[7] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[7]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [7]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[7] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[7] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y5_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_first_bit_number = 8;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .mem_init3 = 2048'hFFFFFFFFFFAAAAAAAAAA555555555540000000003FFFFFFFFFFAAAAAAAAAA555555555540000000000FFFFFFFFFFEAAAAAAAAAA9555555555500000000000FFFFFFFFFFFAAAAAAAAAAA955555555555000000000003FFFFFFFFFFFEAAAAAAAAAAA95555555555550000000000000FFFFFFFFFFFFFAAAAAAAAAAAAAA55555555555555000000000000000FFFFFFFFFFFFFFFFAAAAAAAAAAAAAAAAA55555555555555555500000000000000000000FFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAAAAAAAAAAA55555555555555555555555555555555550000000000000000000000000000000000000000000000000000000000000000000000000000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .mem_init2 = 2048'h00000000000000000000000000000000000000000000000000000000000000000000000000000000015555555555555555555555555555555556AAAAAAAAAAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFC00000000000000000001555555555555555556AAAAAAAAAAAAAAAABFFFFFFFFFFFFFFFC00000000000000155555555555556AAAAAAAAAAAAABFFFFFFFFFFFFC0000000000001555555555555AAAAAAAAAAAAFFFFFFFFFFFF00000000000155555555555AAAAAAAAAAABFFFFFFFFFFC000000000015555555555AAAAAAAAAAAFFFFFFFFFFC000000000055555555556AAAAAAAAABFFFFFFFFFF000000000055555555556AAAAAAAAABFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .mem_init1 = 2048'h00000000005555555555AAAAAAAAAABFFFFFFFFFC00000000005555555555AAAAAAAAAABFFFFFFFFFF0000000000155555555556AAAAAAAAAAFFFFFFFFFFF00000000000555555555556AAAAAAAAAAAFFFFFFFFFFFC000000000001555555555556AAAAAAAAAAAAFFFFFFFFFFFFF000000000000055555555555555AAAAAAAAAAAAAAFFFFFFFFFFFFFFF000000000000000055555555555555555AAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFF0000000000000000000000155555555555555555555555555AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a8 .mem_init0 = 2048'hFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA95555555555555555555555555500000000000000000000003FFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAA9555555555555555540000000000000003FFFFFFFFFFFFFFEAAAAAAAAAAAAA9555555555555540000000000003FFFFFFFFFFFFEAAAAAAAAAAAA555555555555000000000000FFFFFFFFFFFEAAAAAAAAAAA55555555555400000000003FFFFFFFFFFEAAAAAAAAAA5555555555500000000003FFFFFFFFFFAAAAAAAAAA955555555540000000000FFFFFFFFFFAAAAAAAAAA955555555540000000000;
// synopsys translate_on

// Location: LCCOMB_X17_Y8_N4
cycloneive_lcell_comb \u_sel_wave|da_out_reg[8]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[8]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [8]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [8]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[8]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[8]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[8]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X17_Y8_N5
dffeas \u_sel_wave|da_out_reg[8] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[8]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [8]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[8] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[8] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X17_Y8_N10
cycloneive_lcell_comb \u_sel_wave|da_out_reg[9]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[9]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [9]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [9]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[9]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[9]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[9]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X17_Y8_N11
dffeas \u_sel_wave|da_out_reg[9] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[9]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [9]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[9] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[9] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y10_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_first_bit_number = 10;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .mem_init3 = 2048'hFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA55555555555555555555555555555555555555555550000000000000000000000000000000000000000000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA55555555555555555555555555555555555555555555555555555555555555555555555000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .mem_init2 = 2048'h00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000155555555555555555555555555555555555555555555555555555555555555555555556AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000000000000000000000000000000000000000015555555555555555555555555555555555555555556AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .mem_init1 = 2048'h0000000000000000000000000000000000000000155555555555555555555555555555555555555555AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000000000000000000000000000000000000000000055555555555555555555555555555555555555555555555555555555AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a10 .mem_init0 = 2048'hFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA9555555555555555555555555555555555555555555555555555555540000000000000000000000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA95555555555555555555555555555555555555555500000000000000000000000000000000000000000;
// synopsys translate_on

// Location: LCCOMB_X21_Y6_N6
cycloneive_lcell_comb \u_sel_wave|da_out_reg[10]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[10]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [10]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [10]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[10]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[10]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[10]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X21_Y6_N7
dffeas \u_sel_wave|da_out_reg[10] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[10]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [10]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[10] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[10] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X21_Y6_N16
cycloneive_lcell_comb \u_sel_wave|da_out_reg[11]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[11]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [11]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [11]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[11]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[11]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[11]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X21_Y6_N17
dffeas \u_sel_wave|da_out_reg[11] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[11]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [11]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[11] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[11] .power_up = "low";
// synopsys translate_on

// Location: M9K_X15_Y12_N0
cycloneive_ram_block \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 (
	.portawe(vcc),
	.portare(vcc),
	.portaaddrstall(gnd),
	.portbwe(gnd),
	.portbre(vcc),
	.portbaddrstall(gnd),
	.clk0(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.clk1(gnd),
	.ena0(vcc),
	.ena1(vcc),
	.ena2(vcc),
	.ena3(vcc),
	.clr0(gnd),
	.clr1(gnd),
	.portadatain(2'b00),
	.portaaddr({\u_add_32bit|add [31],\u_add_32bit|add [30],\u_add_32bit|add [29],\u_add_32bit|add [28],\u_add_32bit|add [27],\u_add_32bit|add [26],\u_add_32bit|add [25],\u_add_32bit|add [24],\u_add_32bit|add [23],\u_add_32bit|add [22],\u_add_32bit|add [21],\u_add_32bit|add [20]}),
	.portabyteenamasks(1'b1),
	.portbdatain(2'b00),
	.portbaddr(12'b000000000000),
	.portbbyteenamasks(1'b1),
	.devclrn(devclrn),
	.devpor(devpor),
	.portadataout(\u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12_PORTADATAOUT_bus ),
	.portbdataout());
// synopsys translate_off
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .data_interleave_offset_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .data_interleave_width_in_bits = 1;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .init_file = "Sin_Wave.mif";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .init_file_layout = "port_a";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .logical_ram_name = "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .operation_mode = "rom";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_address_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_byte_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_data_out_clear = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_data_out_clock = "clock0";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_first_address = 0;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_first_bit_number = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_last_address = 4095;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_logical_ram_depth = 4096;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_logical_ram_width = 14;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_read_during_write_mode = "new_data_with_nbe_read";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_a_write_enable_clock = "none";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_b_address_width = 12;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .port_b_data_width = 2;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .ram_block_type = "M9K";
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .mem_init3 = 2048'h55555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .mem_init2 = 2048'h00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555555;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .mem_init1 = 2048'hAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF;
defparam \u_ROM_Sin|altsyncram_component|auto_generated|ram_block1a12 .mem_init0 = 2048'hFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA;
// synopsys translate_on

// Location: LCCOMB_X21_Y5_N30
cycloneive_lcell_comb \u_sel_wave|da_out_reg[12]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[12]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [12]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [12]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[12]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[12]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[12]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X21_Y5_N31
dffeas \u_sel_wave|da_out_reg[12] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[12]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [12]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[12] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[12] .power_up = "low";
// synopsys translate_on

// Location: LCCOMB_X21_Y5_N26
cycloneive_lcell_comb \u_sel_wave|da_out_reg[13]~feeder (
// Equation(s):
// \u_sel_wave|da_out_reg[13]~feeder_combout  = \u_ROM_Sin|altsyncram_component|auto_generated|q_a [13]

	.dataa(gnd),
	.datab(gnd),
	.datac(gnd),
	.datad(\u_ROM_Sin|altsyncram_component|auto_generated|q_a [13]),
	.cin(gnd),
	.combout(\u_sel_wave|da_out_reg[13]~feeder_combout ),
	.cout());
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[13]~feeder .lut_mask = 16'hFF00;
defparam \u_sel_wave|da_out_reg[13]~feeder .sum_lutc_input = "datac";
// synopsys translate_on

// Location: FF_X21_Y5_N27
dffeas \u_sel_wave|da_out_reg[13] (
	.clk(\u_PLL_CLK|altpll_component|auto_generated|wire_pll1_clk[0]~clkctrl_outclk ),
	.d(\u_sel_wave|da_out_reg[13]~feeder_combout ),
	.asdata(vcc),
	.clrn(\SYS_RST~inputclkctrl_outclk ),
	.aload(gnd),
	.sclr(gnd),
	.sload(gnd),
	.ena(vcc),
	.devclrn(devclrn),
	.devpor(devpor),
	.q(\u_sel_wave|da_out_reg [13]),
	.prn(vcc));
// synopsys translate_off
defparam \u_sel_wave|da_out_reg[13] .is_wysiwyg = "true";
defparam \u_sel_wave|da_out_reg[13] .power_up = "low";
// synopsys translate_on

// Location: DSPMULT_X20_Y6_N0
cycloneive_mac_mult \Mult0|auto_generated|mac_mult1 (
	.signa(gnd),
	.signb(gnd),
	.clk(gnd),
	.aclr(gnd),
	.ena(vcc),
	.dataa({!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],\u_sel_wave|da_out_reg [12],\u_sel_wave|da_out_reg [11],\u_sel_wave|da_out_reg [10],\u_sel_wave|da_out_reg [9],\u_sel_wave|da_out_reg [8],\u_sel_wave|da_out_reg [7],
\u_sel_wave|da_out_reg [6],\u_sel_wave|da_out_reg [5],\u_sel_wave|da_out_reg [4],\u_sel_wave|da_out_reg [3],\u_sel_wave|da_out_reg [2],\u_sel_wave|da_out_reg [1],\u_sel_wave|da_out_reg [0]}),
	.datab({!spi_data[11],!spi_data[10],spi_data[9],spi_data[8],spi_data[7],spi_data[6],spi_data[5],spi_data[4],spi_data[3],spi_data[2],spi_data[1],spi_data[0],gnd,gnd,gnd,gnd,gnd,gnd}),
	.devclrn(devclrn),
	.devpor(devpor),
	.dataout(\Mult0|auto_generated|mac_mult1_DATAOUT_bus ));
// synopsys translate_off
defparam \Mult0|auto_generated|mac_mult1 .dataa_clock = "none";
defparam \Mult0|auto_generated|mac_mult1 .dataa_width = 18;
defparam \Mult0|auto_generated|mac_mult1 .datab_clock = "none";
defparam \Mult0|auto_generated|mac_mult1 .datab_width = 18;
defparam \Mult0|auto_generated|mac_mult1 .signa_clock = "none";
defparam \Mult0|auto_generated|mac_mult1 .signb_clock = "none";
// synopsys translate_on

// Location: DSPOUT_X20_Y6_N2
cycloneive_mac_out \Mult0|auto_generated|mac_out2 (
	.clk(gnd),
	.aclr(gnd),
	.ena(vcc),
	.dataa({\Mult0|auto_generated|mac_mult1~DATAOUT29 ,\Mult0|auto_generated|mac_mult1~DATAOUT28 ,\Mult0|auto_generated|mac_mult1~DATAOUT27 ,\Mult0|auto_generated|mac_mult1~DATAOUT26 ,\Mult0|auto_generated|mac_mult1~DATAOUT25 ,\Mult0|auto_generated|mac_mult1~DATAOUT24 ,
\Mult0|auto_generated|mac_mult1~DATAOUT23 ,\Mult0|auto_generated|mac_mult1~DATAOUT22 ,\Mult0|auto_generated|mac_mult1~DATAOUT21 ,\Mult0|auto_generated|mac_mult1~DATAOUT20 ,\Mult0|auto_generated|mac_mult1~DATAOUT19 ,\Mult0|auto_generated|mac_mult1~DATAOUT18 ,
\Mult0|auto_generated|mac_mult1~DATAOUT17 ,\Mult0|auto_generated|mac_mult1~DATAOUT16 ,\Mult0|auto_generated|mac_mult1~DATAOUT15 ,\Mult0|auto_generated|mac_mult1~DATAOUT14 ,\Mult0|auto_generated|mac_mult1~DATAOUT13 ,\Mult0|auto_generated|mac_mult1~DATAOUT12 ,
\Mult0|auto_generated|mac_mult1~DATAOUT11 ,\Mult0|auto_generated|mac_mult1~DATAOUT10 ,\Mult0|auto_generated|mac_mult1~DATAOUT9 ,\Mult0|auto_generated|mac_mult1~DATAOUT8 ,\Mult0|auto_generated|mac_mult1~DATAOUT7 ,\Mult0|auto_generated|mac_mult1~DATAOUT6 ,
\Mult0|auto_generated|mac_mult1~DATAOUT5 ,\Mult0|auto_generated|mac_mult1~DATAOUT4 ,\Mult0|auto_generated|mac_mult1~DATAOUT3 ,\Mult0|auto_generated|mac_mult1~DATAOUT2 ,\Mult0|auto_generated|mac_mult1~DATAOUT1 ,\Mult0|auto_generated|mac_mult1~dataout ,
\Mult0|auto_generated|mac_mult1~5 ,\Mult0|auto_generated|mac_mult1~4 ,\Mult0|auto_generated|mac_mult1~3 ,\Mult0|auto_generated|mac_mult1~2 ,\Mult0|auto_generated|mac_mult1~1 ,\Mult0|auto_generated|mac_mult1~0 }),
	.devclrn(devclrn),
	.devpor(devpor),
	.dataout(\Mult0|auto_generated|mac_out2_DATAOUT_bus ));
// synopsys translate_off
defparam \Mult0|auto_generated|mac_out2 .dataa_width = 36;
defparam \Mult0|auto_generated|mac_out2 .output_clock = "none";
// synopsys translate_on

// Location: DSPMULT_X20_Y5_N0
cycloneive_mac_mult \Mult0|auto_generated|mac_mult3 (
	.signa(gnd),
	.signb(gnd),
	.clk(gnd),
	.aclr(gnd),
	.ena(vcc),
	.dataa({!spi_data[11],!spi_data[10],spi_data[9],spi_data[8],spi_data[7],spi_data[6],spi_data[5],spi_data[4],spi_data[3],spi_data[2],spi_data[1],spi_data[0],gnd,gnd,gnd,gnd,gnd,gnd}),
	.datab({!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],!\u_sel_wave|da_out_reg [13],gnd,gnd,gnd,gnd,gnd,gnd,gnd,gnd,gnd,gnd}),
	.devclrn(devclrn),
	.devpor(devpor),
	.dataout(\Mult0|auto_generated|mac_mult3_DATAOUT_bus ));
// synopsys translate_off
defparam \Mult0|auto_generated|mac_mult3 .dataa_clock = "none";
defparam \Mult0|auto_generated|mac_mult3 .dataa_width = 18;
defparam \Mult0|auto_generated|mac_mult3 .datab_clock = "none";
defparam \Mult0|auto_generated|mac_mult3 .datab_width = 18;
defparam \Mult0|auto_generated|mac_mult3 .signa_clock = "none";
defparam \Mult0|auto_generated|mac_mult3 .signb_clock = "none";
// synopsys translate_on

// Location: DSPOUT_X20_Y5_N2
cycloneive_mac_out \Mult0|auto_generated|mac_out4 (
	.clk(gnd),
	.aclr(gnd),
	.ena(vcc),
	.dataa({\Mult0|auto_generated|mac_mult3~DATAOUT19 ,\Mult0|auto_generated|mac_mult3~DATAOUT18 ,\Mult0|auto_generated|mac_mult3~DATAOUT17 ,\Mult0|auto_generated|mac_mult3~DATAOUT16 ,\Mult0|auto_generated|mac_mult3~DATAOUT15 ,\Mult0|auto_generated|mac_mult3~DATAOUT14 ,
\Mult0|auto_generated|mac_mult3~DATAOUT13 ,\Mult0|auto_generated|mac_mult3~DATAOUT12 ,\Mult0|auto_generated|mac_mult3~DATAOUT11 ,\Mult0|auto_generated|mac_mult3~DATAOUT10 ,\Mult0|auto_generated|mac_mult3~DATAOUT9 ,\Mult0|auto_generated|mac_mult3~DATAOUT8 ,
\Mult0|auto_generated|mac_mult3~DATAOUT7 ,\Mult0|auto_generated|mac_mult3~DATAOUT6 ,\Mult0|auto_generated|mac_mult3~DATAOUT5 ,\Mult0|auto_generated|mac_mult3~DATAOUT4 ,\Mult0|auto_generated|mac_mult3~DATAOUT3 ,\Mult0|auto_generated|mac_mult3~DATAOUT2 ,
\Mult0|auto_generated|mac_mult3~DATAOUT1 ,\Mult0|auto_generated|mac_mult3~dataout ,\Mult0|auto_generated|mac_mult3~15 ,\Mult0|auto_generated|mac_mult3~14 ,\Mult0|auto_generated|mac_mult3~13 ,\Mult0|auto_generated|mac_mult3~12 ,\Mult0|auto_generated|mac_mult3~11 ,
\Mult0|auto_generated|mac_mult3~10 ,\Mult0|auto_generated|mac_mult3~9 ,\Mult0|auto_generated|mac_mult3~8 ,\Mult0|auto_generated|mac_mult3~7 ,\Mult0|auto_generated|mac_mult3~6 ,\Mult0|auto_generated|mac_mult3~5 ,\Mult0|auto_generated|mac_mult3~4 ,
\Mult0|auto_generated|mac_mult3~3 ,\Mult0|auto_generated|mac_mult3~2 ,\Mult0|auto_generated|mac_mult3~1 ,\Mult0|auto_generated|mac_mult3~0 }),
	.devclrn(devclrn),
	.devpor(devpor),
	.dataout(\Mult0|auto_generated|mac_out4_DATAOUT_bus ));
// synopsys translate_off
defparam \Mult0|auto_generated|mac_out4 .dataa_width = 36;
defparam \Mult0|auto_generated|mac_out4 .output_clock = "none";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N12
cycloneive_lcell_comb \Mult0|auto_generated|op_1~0 (
// Equation(s):
// \Mult0|auto_generated|op_1~0_combout  = (\Mult0|auto_generated|mac_out2~DATAOUT18  & (\Mult0|auto_generated|mac_out4~dataout  $ (VCC))) # (!\Mult0|auto_generated|mac_out2~DATAOUT18  & (\Mult0|auto_generated|mac_out4~dataout  & VCC))
// \Mult0|auto_generated|op_1~1  = CARRY((\Mult0|auto_generated|mac_out2~DATAOUT18  & \Mult0|auto_generated|mac_out4~dataout ))

	.dataa(\Mult0|auto_generated|mac_out2~DATAOUT18 ),
	.datab(\Mult0|auto_generated|mac_out4~dataout ),
	.datac(gnd),
	.datad(vcc),
	.cin(gnd),
	.combout(\Mult0|auto_generated|op_1~0_combout ),
	.cout(\Mult0|auto_generated|op_1~1 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~0 .lut_mask = 16'h6688;
defparam \Mult0|auto_generated|op_1~0 .sum_lutc_input = "datac";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N14
cycloneive_lcell_comb \Mult0|auto_generated|op_1~2 (
// Equation(s):
// \Mult0|auto_generated|op_1~2_combout  = (\Mult0|auto_generated|mac_out4~DATAOUT1  & ((\Mult0|auto_generated|mac_out2~DATAOUT19  & (\Mult0|auto_generated|op_1~1  & VCC)) # (!\Mult0|auto_generated|mac_out2~DATAOUT19  & (!\Mult0|auto_generated|op_1~1 )))) # 
// (!\Mult0|auto_generated|mac_out4~DATAOUT1  & ((\Mult0|auto_generated|mac_out2~DATAOUT19  & (!\Mult0|auto_generated|op_1~1 )) # (!\Mult0|auto_generated|mac_out2~DATAOUT19  & ((\Mult0|auto_generated|op_1~1 ) # (GND)))))
// \Mult0|auto_generated|op_1~3  = CARRY((\Mult0|auto_generated|mac_out4~DATAOUT1  & (!\Mult0|auto_generated|mac_out2~DATAOUT19  & !\Mult0|auto_generated|op_1~1 )) # (!\Mult0|auto_generated|mac_out4~DATAOUT1  & ((!\Mult0|auto_generated|op_1~1 ) # 
// (!\Mult0|auto_generated|mac_out2~DATAOUT19 ))))

	.dataa(\Mult0|auto_generated|mac_out4~DATAOUT1 ),
	.datab(\Mult0|auto_generated|mac_out2~DATAOUT19 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~1 ),
	.combout(\Mult0|auto_generated|op_1~2_combout ),
	.cout(\Mult0|auto_generated|op_1~3 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~2 .lut_mask = 16'h9617;
defparam \Mult0|auto_generated|op_1~2 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N16
cycloneive_lcell_comb \Mult0|auto_generated|op_1~4 (
// Equation(s):
// \Mult0|auto_generated|op_1~4_combout  = ((\Mult0|auto_generated|mac_out2~DATAOUT20  $ (\Mult0|auto_generated|mac_out4~DATAOUT2  $ (!\Mult0|auto_generated|op_1~3 )))) # (GND)
// \Mult0|auto_generated|op_1~5  = CARRY((\Mult0|auto_generated|mac_out2~DATAOUT20  & ((\Mult0|auto_generated|mac_out4~DATAOUT2 ) # (!\Mult0|auto_generated|op_1~3 ))) # (!\Mult0|auto_generated|mac_out2~DATAOUT20  & (\Mult0|auto_generated|mac_out4~DATAOUT2  & 
// !\Mult0|auto_generated|op_1~3 )))

	.dataa(\Mult0|auto_generated|mac_out2~DATAOUT20 ),
	.datab(\Mult0|auto_generated|mac_out4~DATAOUT2 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~3 ),
	.combout(\Mult0|auto_generated|op_1~4_combout ),
	.cout(\Mult0|auto_generated|op_1~5 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~4 .lut_mask = 16'h698E;
defparam \Mult0|auto_generated|op_1~4 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N18
cycloneive_lcell_comb \Mult0|auto_generated|op_1~6 (
// Equation(s):
// \Mult0|auto_generated|op_1~6_combout  = (\Mult0|auto_generated|mac_out2~DATAOUT21  & ((\Mult0|auto_generated|mac_out4~DATAOUT3  & (\Mult0|auto_generated|op_1~5  & VCC)) # (!\Mult0|auto_generated|mac_out4~DATAOUT3  & (!\Mult0|auto_generated|op_1~5 )))) # 
// (!\Mult0|auto_generated|mac_out2~DATAOUT21  & ((\Mult0|auto_generated|mac_out4~DATAOUT3  & (!\Mult0|auto_generated|op_1~5 )) # (!\Mult0|auto_generated|mac_out4~DATAOUT3  & ((\Mult0|auto_generated|op_1~5 ) # (GND)))))
// \Mult0|auto_generated|op_1~7  = CARRY((\Mult0|auto_generated|mac_out2~DATAOUT21  & (!\Mult0|auto_generated|mac_out4~DATAOUT3  & !\Mult0|auto_generated|op_1~5 )) # (!\Mult0|auto_generated|mac_out2~DATAOUT21  & ((!\Mult0|auto_generated|op_1~5 ) # 
// (!\Mult0|auto_generated|mac_out4~DATAOUT3 ))))

	.dataa(\Mult0|auto_generated|mac_out2~DATAOUT21 ),
	.datab(\Mult0|auto_generated|mac_out4~DATAOUT3 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~5 ),
	.combout(\Mult0|auto_generated|op_1~6_combout ),
	.cout(\Mult0|auto_generated|op_1~7 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~6 .lut_mask = 16'h9617;
defparam \Mult0|auto_generated|op_1~6 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N20
cycloneive_lcell_comb \Mult0|auto_generated|op_1~8 (
// Equation(s):
// \Mult0|auto_generated|op_1~8_combout  = ((\Mult0|auto_generated|mac_out2~DATAOUT22  $ (\Mult0|auto_generated|mac_out4~DATAOUT4  $ (!\Mult0|auto_generated|op_1~7 )))) # (GND)
// \Mult0|auto_generated|op_1~9  = CARRY((\Mult0|auto_generated|mac_out2~DATAOUT22  & ((\Mult0|auto_generated|mac_out4~DATAOUT4 ) # (!\Mult0|auto_generated|op_1~7 ))) # (!\Mult0|auto_generated|mac_out2~DATAOUT22  & (\Mult0|auto_generated|mac_out4~DATAOUT4  & 
// !\Mult0|auto_generated|op_1~7 )))

	.dataa(\Mult0|auto_generated|mac_out2~DATAOUT22 ),
	.datab(\Mult0|auto_generated|mac_out4~DATAOUT4 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~7 ),
	.combout(\Mult0|auto_generated|op_1~8_combout ),
	.cout(\Mult0|auto_generated|op_1~9 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~8 .lut_mask = 16'h698E;
defparam \Mult0|auto_generated|op_1~8 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N22
cycloneive_lcell_comb \Mult0|auto_generated|op_1~10 (
// Equation(s):
// \Mult0|auto_generated|op_1~10_combout  = (\Mult0|auto_generated|mac_out4~DATAOUT5  & ((\Mult0|auto_generated|mac_out2~DATAOUT23  & (\Mult0|auto_generated|op_1~9  & VCC)) # (!\Mult0|auto_generated|mac_out2~DATAOUT23  & (!\Mult0|auto_generated|op_1~9 )))) # 
// (!\Mult0|auto_generated|mac_out4~DATAOUT5  & ((\Mult0|auto_generated|mac_out2~DATAOUT23  & (!\Mult0|auto_generated|op_1~9 )) # (!\Mult0|auto_generated|mac_out2~DATAOUT23  & ((\Mult0|auto_generated|op_1~9 ) # (GND)))))
// \Mult0|auto_generated|op_1~11  = CARRY((\Mult0|auto_generated|mac_out4~DATAOUT5  & (!\Mult0|auto_generated|mac_out2~DATAOUT23  & !\Mult0|auto_generated|op_1~9 )) # (!\Mult0|auto_generated|mac_out4~DATAOUT5  & ((!\Mult0|auto_generated|op_1~9 ) # 
// (!\Mult0|auto_generated|mac_out2~DATAOUT23 ))))

	.dataa(\Mult0|auto_generated|mac_out4~DATAOUT5 ),
	.datab(\Mult0|auto_generated|mac_out2~DATAOUT23 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~9 ),
	.combout(\Mult0|auto_generated|op_1~10_combout ),
	.cout(\Mult0|auto_generated|op_1~11 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~10 .lut_mask = 16'h9617;
defparam \Mult0|auto_generated|op_1~10 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N24
cycloneive_lcell_comb \Mult0|auto_generated|op_1~12 (
// Equation(s):
// \Mult0|auto_generated|op_1~12_combout  = ((\Mult0|auto_generated|mac_out4~DATAOUT6  $ (\Mult0|auto_generated|mac_out2~DATAOUT24  $ (!\Mult0|auto_generated|op_1~11 )))) # (GND)
// \Mult0|auto_generated|op_1~13  = CARRY((\Mult0|auto_generated|mac_out4~DATAOUT6  & ((\Mult0|auto_generated|mac_out2~DATAOUT24 ) # (!\Mult0|auto_generated|op_1~11 ))) # (!\Mult0|auto_generated|mac_out4~DATAOUT6  & (\Mult0|auto_generated|mac_out2~DATAOUT24  
// & !\Mult0|auto_generated|op_1~11 )))

	.dataa(\Mult0|auto_generated|mac_out4~DATAOUT6 ),
	.datab(\Mult0|auto_generated|mac_out2~DATAOUT24 ),
	.datac(gnd),
	.datad(vcc),
	.cin(\Mult0|auto_generated|op_1~11 ),
	.combout(\Mult0|auto_generated|op_1~12_combout ),
	.cout(\Mult0|auto_generated|op_1~13 ));
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~12 .lut_mask = 16'h698E;
defparam \Mult0|auto_generated|op_1~12 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: LCCOMB_X24_Y9_N26
cycloneive_lcell_comb \Mult0|auto_generated|op_1~14 (
// Equation(s):
// \Mult0|auto_generated|op_1~14_combout  = \Mult0|auto_generated|mac_out4~DATAOUT7  $ (\Mult0|auto_generated|op_1~13  $ (\Mult0|auto_generated|mac_out2~DATAOUT25 ))

	.dataa(gnd),
	.datab(\Mult0|auto_generated|mac_out4~DATAOUT7 ),
	.datac(gnd),
	.datad(\Mult0|auto_generated|mac_out2~DATAOUT25 ),
	.cin(\Mult0|auto_generated|op_1~13 ),
	.combout(\Mult0|auto_generated|op_1~14_combout ),
	.cout());
// synopsys translate_off
defparam \Mult0|auto_generated|op_1~14 .lut_mask = 16'hC33C;
defparam \Mult0|auto_generated|op_1~14 .sum_lutc_input = "cin";
// synopsys translate_on

// Location: IOIBUF_X34_Y12_N8
cycloneive_io_ibuf \KEY_IN[0]~input (
	.i(KEY_IN[0]),
	.ibar(gnd),
	.o(\KEY_IN[0]~input_o ));
// synopsys translate_off
defparam \KEY_IN[0]~input .bus_hold = "false";
defparam \KEY_IN[0]~input .simulate_z_as = "z";
// synopsys translate_on

// Location: IOIBUF_X34_Y12_N15
cycloneive_io_ibuf \KEY_IN[1]~input (
	.i(KEY_IN[1]),
	.ibar(gnd),
	.o(\KEY_IN[1]~input_o ));
// synopsys translate_off
defparam \KEY_IN[1]~input .bus_hold = "false";
defparam \KEY_IN[1]~input .simulate_z_as = "z";
// synopsys translate_on

// Location: IOIBUF_X34_Y12_N1
cycloneive_io_ibuf \KEY_IN[2]~input (
	.i(KEY_IN[2]),
	.ibar(gnd),
	.o(\KEY_IN[2]~input_o ));
// synopsys translate_off
defparam \KEY_IN[2]~input .bus_hold = "false";
defparam \KEY_IN[2]~input .simulate_z_as = "z";
// synopsys translate_on

endmodule
