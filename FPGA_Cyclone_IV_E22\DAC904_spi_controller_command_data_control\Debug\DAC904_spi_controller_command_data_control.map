******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Fri Aug  1 01:35:08 2025

OUTPUT FILE NAME:   <DAC904_spi_controller_command_data_control.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000004cd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000007b8  0001f848  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000007b8   000007b8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000006d0   000006d0    r-x .text
  00000790    00000790    00000010   00000010    r-- .rodata
  000007a0    000007a0    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000006d0     
                  000000c0    000000c4     spi_controller_command_data_control.o (.text.send_dac_params)
                  00000184    000000c0     spi_controller_command_data_control.o (.text.main)
                  00000244    0000008c     spi_controller_command_data_control.o (.text.delay_ms_safe)
                  000002d0    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000320    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_0_init)
                  00000370    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  000003b4    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000003f8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000438    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000474    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  000004a4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000004cc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004f4    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00000518    00000024     ti_msp_dl_config.o (.text.DL_SPI_setControllerCommandDataModeConfig)
                  0000053c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000055c    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000057c    0000001e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000059a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000059c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000005b8    0000001c     ti_msp_dl_config.o (.text.DL_SPI_enableControllerCommandDataMode)
                  000005d4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000005f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000608    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000620    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000638    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000650    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00000668    00000018     spi_controller_command_data_control.o (.text.DL_GPIO_togglePins)
                  00000680    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00000698    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000006b0    00000018     spi_controller_command_data_control.o (.text.DL_SPI_isBusy)
                  000006c8    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000006e0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000006f8    00000016     spi_controller_command_data_control.o (.text.DL_SPI_transmitData8)
                  0000070e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000724    00000014     spi_controller_command_data_control.o (.text.DL_GPIO_clearPins)
                  00000738    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000074c    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000075e    00000002     --HOLE-- [fill = 0]
                  00000760    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00000770    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000077a    00000006     libc.a : exit.c.obj (.text:abort)
                  00000780    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000784    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000788    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000078c    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000007a0    00000018     
                  000007a0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000007a8    00000004     (__TI_handler_table)
                  000007ac    00000008     (__TI_cinit_table)
                  000007b4    00000004     --HOLE-- [fill = 0]

.rodata    0    00000790    00000010     
                  00000790    0000000a     ti_msp_dl_config.o (.rodata.gSPI_0_config)
                  0000079a    00000002     ti_msp_dl_config.o (.rodata.gSPI_0_clockConfig)
                  0000079c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_0Backup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      818    12        40     
       spi_controller_command_data_control.o   618    0         0      
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1444   204       40     
                                                                       
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_spi.o                                86     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  96     0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       copy_zero_init.c.obj                    22     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  132    0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj                    64     0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  66     0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      20        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            1742   224       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000007ac records: 1, size/record: 8, table size: 8
	.bss: load addr=000007a0, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000007a8 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000781  ADC0_IRQHandler               
00000781  ADC1_IRQHandler               
00000781  AES_IRQHandler                
00000784  C$$EXIT                       
00000781  CANFD0_IRQHandler             
00000781  DAC0_IRQHandler               
00000771  DL_Common_delayCycles         
00000371  DL_SPI_init                   
0000074d  DL_SPI_setClockConfig         
00000781  DMA_IRQHandler                
00000781  Default_Handler               
00000781  GROUP0_IRQHandler             
00000781  GROUP1_IRQHandler             
00000785  HOSTexit                      
00000781  HardFault_Handler             
00000781  I2C0_IRQHandler               
00000781  I2C1_IRQHandler               
00000781  NMI_Handler                   
00000781  PendSV_Handler                
00000781  RTC_IRQHandler                
00000789  Reset_Handler                 
00000781  SPI0_IRQHandler               
00000781  SPI1_IRQHandler               
00000781  SVC_Handler                   
000002d1  SYSCFG_DL_GPIO_init           
00000321  SYSCFG_DL_SPI_0_init          
0000057d  SYSCFG_DL_SYSCTL_init         
0000055d  SYSCFG_DL_init                
000003b5  SYSCFG_DL_initPower           
00000781  SysTick_Handler               
00000781  TIMA0_IRQHandler              
00000781  TIMA1_IRQHandler              
00000781  TIMG0_IRQHandler              
00000781  TIMG12_IRQHandler             
00000781  TIMG6_IRQHandler              
00000781  TIMG7_IRQHandler              
00000781  TIMG8_IRQHandler              
00000781  UART0_IRQHandler              
00000781  UART1_IRQHandler              
00000781  UART2_IRQHandler              
00000781  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000007ac  __TI_CINIT_Base               
000007b4  __TI_CINIT_Limit              
000007b4  __TI_CINIT_Warm               
000007a8  __TI_Handler_Table_Base       
000007ac  __TI_Handler_Table_Limit      
00000439  __TI_auto_init_nobinit_nopinit
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
0000070f  __TI_zero_init_nomemset       
0000059b  __aeabi_idiv0                 
000003f9  __aeabi_uidiv                 
000003f9  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000004cd  _c_int00_noargs               
UNDEFED   _system_post_cinit            
0000078d  _system_pre_init              
0000077b  abort                         
ffffffff  binit                         
00000245  delay_ms_safe                 
20200000  gSPI_0Backup                  
00000000  interruptVectors              
00000185  main                          
000000c1  send_dac_params               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  send_dac_params               
00000185  main                          
00000200  __STACK_SIZE                  
00000245  delay_ms_safe                 
000002d1  SYSCFG_DL_GPIO_init           
00000321  SYSCFG_DL_SPI_0_init          
00000371  DL_SPI_init                   
000003b5  SYSCFG_DL_initPower           
000003f9  __aeabi_uidiv                 
000003f9  __aeabi_uidivmod              
00000439  __TI_auto_init_nobinit_nopinit
000004cd  _c_int00_noargs               
0000055d  SYSCFG_DL_init                
0000057d  SYSCFG_DL_SYSCTL_init         
0000059b  __aeabi_idiv0                 
0000070f  __TI_zero_init_nomemset       
0000074d  DL_SPI_setClockConfig         
00000771  DL_Common_delayCycles         
0000077b  abort                         
00000781  ADC0_IRQHandler               
00000781  ADC1_IRQHandler               
00000781  AES_IRQHandler                
00000781  CANFD0_IRQHandler             
00000781  DAC0_IRQHandler               
00000781  DMA_IRQHandler                
00000781  Default_Handler               
00000781  GROUP0_IRQHandler             
00000781  GROUP1_IRQHandler             
00000781  HardFault_Handler             
00000781  I2C0_IRQHandler               
00000781  I2C1_IRQHandler               
00000781  NMI_Handler                   
00000781  PendSV_Handler                
00000781  RTC_IRQHandler                
00000781  SPI0_IRQHandler               
00000781  SPI1_IRQHandler               
00000781  SVC_Handler                   
00000781  SysTick_Handler               
00000781  TIMA0_IRQHandler              
00000781  TIMA1_IRQHandler              
00000781  TIMG0_IRQHandler              
00000781  TIMG12_IRQHandler             
00000781  TIMG6_IRQHandler              
00000781  TIMG7_IRQHandler              
00000781  TIMG8_IRQHandler              
00000781  UART0_IRQHandler              
00000781  UART1_IRQHandler              
00000781  UART2_IRQHandler              
00000781  UART3_IRQHandler              
00000784  C$$EXIT                       
00000785  HOSTexit                      
00000789  Reset_Handler                 
0000078d  _system_pre_init              
000007a8  __TI_Handler_Table_Base       
000007ac  __TI_CINIT_Base               
000007ac  __TI_Handler_Table_Limit      
000007b4  __TI_CINIT_Limit              
000007b4  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gSPI_0Backup                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[83 symbols]
