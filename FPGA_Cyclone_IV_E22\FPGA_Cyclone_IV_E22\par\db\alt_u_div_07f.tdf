--alt_u_div DEVICE_FAMILY="Cyclone IV E" LPM_PIPELINE=0 MAXIMIZE_SPEED=5 SKIP_BITS=0 WIDTH_D=2 WIDTH_N=14 WIDTH_Q=14 WIDTH_R=2 denominator numerator quotient remainder
--VERSION_BEGIN 13.1 cbx_cycloneii 2013:10:23:18:05:48:SJ cbx_lpm_abs 2013:10:23:18:05:48:SJ cbx_lpm_add_sub 2013:10:23:18:05:48:SJ cbx_lpm_divide 2013:10:23:18:05:48:SJ cbx_mgl 2013:10:23:18:06:54:SJ cbx_stratix 2013:10:23:18:05:48:SJ cbx_stratixii 2013:10:23:18:05:48:SJ cbx_util_mgl 2013:10:23:18:05:48:SJ  VERSION_END


-- Copyright (C) 1991-2013 Altera Corporation
--  Your use of Altera Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Altera Program License 
--  Subscription Agreement, Altera MegaCore Function License 
--  Agreement, or other applicable license agreement, including, 
--  without limitation, that your use is for the sole purpose of 
--  programming logic devices manufactured by Altera and sold by 
--  Altera or its authorized distributors.  Please refer to the 
--  applicable agreement for further details.


FUNCTION add_sub_7pc (dataa[0..0], datab[0..0])
RETURNS ( cout, result[0..0]);
FUNCTION add_sub_8pc (dataa[1..0], datab[1..0])
RETURNS ( cout, result[1..0]);

--synthesis_resources = lut 48 
SUBDESIGN alt_u_div_07f
( 
	denominator[1..0]	:	input;
	numerator[13..0]	:	input;
	quotient[13..0]	:	output;
	remainder[1..0]	:	output;
) 
VARIABLE 
	add_sub_0 : add_sub_7pc;
	add_sub_1 : add_sub_8pc;
	add_sub_10_result_int[3..0]	:	WIRE;
	add_sub_10_cout	:	WIRE;
	add_sub_10_dataa[2..0]	:	WIRE;
	add_sub_10_datab[2..0]	:	WIRE;
	add_sub_10_result[2..0]	:	WIRE;
	add_sub_11_result_int[3..0]	:	WIRE;
	add_sub_11_cout	:	WIRE;
	add_sub_11_dataa[2..0]	:	WIRE;
	add_sub_11_datab[2..0]	:	WIRE;
	add_sub_11_result[2..0]	:	WIRE;
	add_sub_12_result_int[3..0]	:	WIRE;
	add_sub_12_cout	:	WIRE;
	add_sub_12_dataa[2..0]	:	WIRE;
	add_sub_12_datab[2..0]	:	WIRE;
	add_sub_12_result[2..0]	:	WIRE;
	add_sub_13_result_int[3..0]	:	WIRE;
	add_sub_13_cout	:	WIRE;
	add_sub_13_dataa[2..0]	:	WIRE;
	add_sub_13_datab[2..0]	:	WIRE;
	add_sub_13_result[2..0]	:	WIRE;
	add_sub_2_result_int[3..0]	:	WIRE;
	add_sub_2_cout	:	WIRE;
	add_sub_2_dataa[2..0]	:	WIRE;
	add_sub_2_datab[2..0]	:	WIRE;
	add_sub_2_result[2..0]	:	WIRE;
	add_sub_3_result_int[3..0]	:	WIRE;
	add_sub_3_cout	:	WIRE;
	add_sub_3_dataa[2..0]	:	WIRE;
	add_sub_3_datab[2..0]	:	WIRE;
	add_sub_3_result[2..0]	:	WIRE;
	add_sub_4_result_int[3..0]	:	WIRE;
	add_sub_4_cout	:	WIRE;
	add_sub_4_dataa[2..0]	:	WIRE;
	add_sub_4_datab[2..0]	:	WIRE;
	add_sub_4_result[2..0]	:	WIRE;
	add_sub_5_result_int[3..0]	:	WIRE;
	add_sub_5_cout	:	WIRE;
	add_sub_5_dataa[2..0]	:	WIRE;
	add_sub_5_datab[2..0]	:	WIRE;
	add_sub_5_result[2..0]	:	WIRE;
	add_sub_6_result_int[3..0]	:	WIRE;
	add_sub_6_cout	:	WIRE;
	add_sub_6_dataa[2..0]	:	WIRE;
	add_sub_6_datab[2..0]	:	WIRE;
	add_sub_6_result[2..0]	:	WIRE;
	add_sub_7_result_int[3..0]	:	WIRE;
	add_sub_7_cout	:	WIRE;
	add_sub_7_dataa[2..0]	:	WIRE;
	add_sub_7_datab[2..0]	:	WIRE;
	add_sub_7_result[2..0]	:	WIRE;
	add_sub_8_result_int[3..0]	:	WIRE;
	add_sub_8_cout	:	WIRE;
	add_sub_8_dataa[2..0]	:	WIRE;
	add_sub_8_datab[2..0]	:	WIRE;
	add_sub_8_result[2..0]	:	WIRE;
	add_sub_9_result_int[3..0]	:	WIRE;
	add_sub_9_cout	:	WIRE;
	add_sub_9_dataa[2..0]	:	WIRE;
	add_sub_9_datab[2..0]	:	WIRE;
	add_sub_9_result[2..0]	:	WIRE;
	DenominatorIn[44..0]	: WIRE;
	DenominatorIn_tmp[44..0]	: WIRE;
	gnd_wire	: WIRE;
	nose[209..0]	: WIRE;
	NumeratorIn[209..0]	: WIRE;
	NumeratorIn_tmp[209..0]	: WIRE;
	prestg[41..0]	: WIRE;
	quotient_tmp[13..0]	: WIRE;
	sel[29..0]	: WIRE;
	selnose[209..0]	: WIRE;
	StageIn[44..0]	: WIRE;
	StageIn_tmp[44..0]	: WIRE;
	StageOut[41..0]	: WIRE;

BEGIN 
	add_sub_0.dataa[0..0] = NumeratorIn[13..13];
	add_sub_0.datab[0..0] = DenominatorIn[0..0];
	add_sub_1.dataa[] = ( StageIn[3..3], NumeratorIn[26..26]);
	add_sub_1.datab[1..0] = DenominatorIn[4..3];
	add_sub_10_result_int[] = (0, add_sub_10_dataa[]) - (0, add_sub_10_datab[]);
	add_sub_10_result[] = add_sub_10_result_int[2..0];
	add_sub_10_cout = !add_sub_10_result_int[3];
	add_sub_10_dataa[] = ( StageIn[31..30], NumeratorIn[143..143]);
	add_sub_10_datab[] = DenominatorIn[32..30];
	add_sub_11_result_int[] = (0, add_sub_11_dataa[]) - (0, add_sub_11_datab[]);
	add_sub_11_result[] = add_sub_11_result_int[2..0];
	add_sub_11_cout = !add_sub_11_result_int[3];
	add_sub_11_dataa[] = ( StageIn[34..33], NumeratorIn[156..156]);
	add_sub_11_datab[] = DenominatorIn[35..33];
	add_sub_12_result_int[] = (0, add_sub_12_dataa[]) - (0, add_sub_12_datab[]);
	add_sub_12_result[] = add_sub_12_result_int[2..0];
	add_sub_12_cout = !add_sub_12_result_int[3];
	add_sub_12_dataa[] = ( StageIn[37..36], NumeratorIn[169..169]);
	add_sub_12_datab[] = DenominatorIn[38..36];
	add_sub_13_result_int[] = (0, add_sub_13_dataa[]) - (0, add_sub_13_datab[]);
	add_sub_13_result[] = add_sub_13_result_int[2..0];
	add_sub_13_cout = !add_sub_13_result_int[3];
	add_sub_13_dataa[] = ( StageIn[40..39], NumeratorIn[182..182]);
	add_sub_13_datab[] = DenominatorIn[41..39];
	add_sub_2_result_int[] = (0, add_sub_2_dataa[]) - (0, add_sub_2_datab[]);
	add_sub_2_result[] = add_sub_2_result_int[2..0];
	add_sub_2_cout = !add_sub_2_result_int[3];
	add_sub_2_dataa[] = ( StageIn[7..6], NumeratorIn[39..39]);
	add_sub_2_datab[] = DenominatorIn[8..6];
	add_sub_3_result_int[] = (0, add_sub_3_dataa[]) - (0, add_sub_3_datab[]);
	add_sub_3_result[] = add_sub_3_result_int[2..0];
	add_sub_3_cout = !add_sub_3_result_int[3];
	add_sub_3_dataa[] = ( StageIn[10..9], NumeratorIn[52..52]);
	add_sub_3_datab[] = DenominatorIn[11..9];
	add_sub_4_result_int[] = (0, add_sub_4_dataa[]) - (0, add_sub_4_datab[]);
	add_sub_4_result[] = add_sub_4_result_int[2..0];
	add_sub_4_cout = !add_sub_4_result_int[3];
	add_sub_4_dataa[] = ( StageIn[13..12], NumeratorIn[65..65]);
	add_sub_4_datab[] = DenominatorIn[14..12];
	add_sub_5_result_int[] = (0, add_sub_5_dataa[]) - (0, add_sub_5_datab[]);
	add_sub_5_result[] = add_sub_5_result_int[2..0];
	add_sub_5_cout = !add_sub_5_result_int[3];
	add_sub_5_dataa[] = ( StageIn[16..15], NumeratorIn[78..78]);
	add_sub_5_datab[] = DenominatorIn[17..15];
	add_sub_6_result_int[] = (0, add_sub_6_dataa[]) - (0, add_sub_6_datab[]);
	add_sub_6_result[] = add_sub_6_result_int[2..0];
	add_sub_6_cout = !add_sub_6_result_int[3];
	add_sub_6_dataa[] = ( StageIn[19..18], NumeratorIn[91..91]);
	add_sub_6_datab[] = DenominatorIn[20..18];
	add_sub_7_result_int[] = (0, add_sub_7_dataa[]) - (0, add_sub_7_datab[]);
	add_sub_7_result[] = add_sub_7_result_int[2..0];
	add_sub_7_cout = !add_sub_7_result_int[3];
	add_sub_7_dataa[] = ( StageIn[22..21], NumeratorIn[104..104]);
	add_sub_7_datab[] = DenominatorIn[23..21];
	add_sub_8_result_int[] = (0, add_sub_8_dataa[]) - (0, add_sub_8_datab[]);
	add_sub_8_result[] = add_sub_8_result_int[2..0];
	add_sub_8_cout = !add_sub_8_result_int[3];
	add_sub_8_dataa[] = ( StageIn[25..24], NumeratorIn[117..117]);
	add_sub_8_datab[] = DenominatorIn[26..24];
	add_sub_9_result_int[] = (0, add_sub_9_dataa[]) - (0, add_sub_9_datab[]);
	add_sub_9_result[] = add_sub_9_result_int[2..0];
	add_sub_9_cout = !add_sub_9_result_int[3];
	add_sub_9_dataa[] = ( StageIn[28..27], NumeratorIn[130..130]);
	add_sub_9_datab[] = DenominatorIn[29..27];
	DenominatorIn[] = DenominatorIn_tmp[];
	DenominatorIn_tmp[] = ( DenominatorIn[41..0], ( gnd_wire, denominator[]));
	gnd_wire = B"0";
	nose[] = ( B"00000000000000", add_sub_13_cout, B"00000000000000", add_sub_12_cout, B"00000000000000", add_sub_11_cout, B"00000000000000", add_sub_10_cout, B"00000000000000", add_sub_9_cout, B"00000000000000", add_sub_8_cout, B"00000000000000", add_sub_7_cout, B"00000000000000", add_sub_6_cout, B"00000000000000", add_sub_5_cout, B"00000000000000", add_sub_4_cout, B"00000000000000", add_sub_3_cout, B"00000000000000", add_sub_2_cout, B"00000000000000", add_sub_1.cout, B"00000000000000", add_sub_0.cout);
	NumeratorIn[] = NumeratorIn_tmp[];
	NumeratorIn_tmp[] = ( NumeratorIn[195..0], numerator[]);
	prestg[] = ( add_sub_13_result[], add_sub_12_result[], add_sub_11_result[], add_sub_10_result[], add_sub_9_result[], add_sub_8_result[], add_sub_7_result[], add_sub_6_result[], add_sub_5_result[], add_sub_4_result[], add_sub_3_result[], add_sub_2_result[], GND, add_sub_1.result[], B"00", add_sub_0.result[]);
	quotient[] = quotient_tmp[];
	quotient_tmp[] = ( (! selnose[0..0]), (! selnose[15..15]), (! selnose[30..30]), (! selnose[45..45]), (! selnose[60..60]), (! selnose[75..75]), (! selnose[90..90]), (! selnose[105..105]), (! selnose[120..120]), (! selnose[135..135]), (! selnose[150..150]), (! selnose[165..165]), (! selnose[180..180]), (! selnose[195..195]));
	remainder[1..0] = StageIn[43..42];
	sel[] = ( gnd_wire, (sel[29..29] # DenominatorIn[43..43]), gnd_wire, (sel[27..27] # DenominatorIn[40..40]), gnd_wire, (sel[25..25] # DenominatorIn[37..37]), gnd_wire, (sel[23..23] # DenominatorIn[34..34]), gnd_wire, (sel[21..21] # DenominatorIn[31..31]), gnd_wire, (sel[19..19] # DenominatorIn[28..28]), gnd_wire, (sel[17..17] # DenominatorIn[25..25]), gnd_wire, (sel[15..15] # DenominatorIn[22..22]), gnd_wire, (sel[13..13] # DenominatorIn[19..19]), gnd_wire, (sel[11..11] # DenominatorIn[16..16]), gnd_wire, (sel[9..9] # DenominatorIn[13..13]), gnd_wire, (sel[7..7] # DenominatorIn[10..10]), gnd_wire, (sel[5..5] # DenominatorIn[7..7]), gnd_wire, (sel[3..3] # DenominatorIn[4..4]), gnd_wire, (sel[1..1] # DenominatorIn[1..1]));
	selnose[] = ( (! nose[209..209]), (! nose[208..208]), (! nose[207..207]), (! nose[206..206]), (! nose[205..205]), (! nose[204..204]), (! nose[203..203]), (! nose[202..202]), (! nose[201..201]), (! nose[200..200]), (! nose[199..199]), (! nose[198..198]), ((! nose[197..197]) # sel[29..29]), ((! nose[196..196]) # sel[28..28]), (! nose[195..195]), (! nose[194..194]), (! nose[193..193]), (! nose[192..192]), (! nose[191..191]), (! nose[190..190]), (! nose[189..189]), (! nose[188..188]), (! nose[187..187]), (! nose[186..186]), (! nose[185..185]), (! nose[184..184]), ((! nose[183..183]) # sel[27..27]), ((! nose[182..182]) # sel[26..26]), (! nose[181..181]), (! nose[180..180]), (! nose[179..179]), (! nose[178..178]), (! nose[177..177]), (! nose[176..176]), (! nose[175..175]), (! nose[174..174]), (! nose[173..173]), (! nose[172..172]), (! nose[171..171]), (! nose[170..170]), ((! nose[169..169]) # sel[25..25]), ((! nose[168..168]) # sel[24..24]), (! nose[167..167]), (! nose[166..166]), (! nose[165..165]), (! nose[164..164]), (! nose[163..163]), (! nose[162..162]), (! nose[161..161]), (! nose[160..160]), (! nose[159..159]), (! nose[158..158]), (! nose[157..157]), (! nose[156..156]), ((! nose[155..155]) # sel[23..23]), ((! nose[154..154]) # sel[22..22]), (! nose[153..153]), (! nose[152..152]), (! nose[151..151]), (! nose[150..150]), (! nose[149..149]), (! nose[148..148]), (! nose[147..147]), (! nose[146..146]), (! nose[145..145]), (! nose[144..144]), (! nose[143..143]), (! nose[142..142]), ((! nose[141..141]) # sel[21..21]), ((! nose[140..140]) # sel[20..20]), (! nose[139..139]), (! nose[138..138]), (! nose[137..137]), (! nose[136..136]), (! nose[135..135]), (! nose[134..134]), (! nose[133..133]), (! nose[132..132]), (! nose[131..131]), (! nose[130..130]), (! nose[129..129]), (! nose[128..128]), ((! nose[127..127]) # sel[19..19]), ((! nose[126..126]) # sel[18..18]), (! nose[125..125]), (! nose[124..124]), (! nose[123..123]), (! nose[122..122]), (! nose[121..121]), (! nose[120..120]), (! nose[119..119]), (! nose[118..118]), (! nose[117..117]), (! nose[116..116]), (! nose[115..115]), (! nose[114..114]), ((! nose[113..113]) # sel[17..17]), ((! nose[112..112]) # sel[16..16]), (! nose[111..111]), (! nose[110..110]), (! nose[109..109]), (! nose[108..108]), (! nose[107..107]), (! nose[106..106]), (! nose[105..105]), (! nose[104..104]), (! nose[103..103]), (! nose[102..102]), (! nose[101..101]), (! nose[100..100]), ((! nose[99..99]) # sel[15..15]), ((! nose[98..98]) # sel[14..14]), (! nose[97..97]), (! nose[96..96]), (! nose[95..95]), (! nose[94..94]), (! nose[93..93]), (! nose[92..92]), (! nose[91..91]), (! nose[90..90]), (! nose[89..89]), (! nose[88..88]), (! nose[87..87]), (! nose[86..86]), ((! nose[85..85]) # sel[13..13]), ((! nose[84..84]) # sel[12..12]), (! nose[83..83]), (! nose[82..82]), (! nose[81..81]), (! nose[80..80]), (! nose[79..79]), (! nose[78..78]), (! nose[77..77]), (! nose[76..76]), (! nose[75..75]), (! nose[74..74]), (! nose[73..73]), (! nose[72..72]), ((! nose[71..71]) # sel[11..11]), ((! nose[70..70]) # sel[10..10]), (! nose[69..69]), (! nose[68..68]), (! nose[67..67]), (! nose[66..66]), (! nose[65..65]), (! nose[64..64]), (! nose[63..63]), (! nose[62..62]), (! nose[61..61]), (! nose[60..60]), (! nose[59..59]), (! nose[58..58]), ((! nose[57..57]) # sel[9..9]), ((! nose[56..56]) # sel[8..8]), (! nose[55..55]), (! nose[54..54]), (! nose[53..53]), (! nose[52..52]), (! nose[51..51]), (! nose[50..50]), (! nose[49..49]), (! nose[48..48]), (! nose[47..47]), (! nose[46..46]), (! nose[45..45]), (! nose[44..44]), ((! nose[43..43]) # sel[7..7]), ((! nose[42..42]) # sel[6..6]), (! nose[41..41]), (! nose[40..40]), (! nose[39..39]), (! nose[38..38]), (! nose[37..37]), (! nose[36..36]), (! nose[35..35]), (! nose[34..34]), (! nose[33..33]), (! nose[32..32]), (! nose[31..31]), (! nose[30..30]), ((! nose[29..29]) # sel[5..5]), ((! nose[28..28]) # sel[4..4]), (! nose[27..27]), (! nose[26..26]), (! nose[25..25]), (! nose[24..24]), (! nose[23..23]), (! nose[22..22]), (! nose[21..21]), (! nose[20..20]), (! nose[19..19]), (! nose[18..18]), (! nose[17..17]), (! nose[16..16]), ((! nose[15..15]) # sel[3..3]), ((! nose[14..14]) # sel[2..2]), (! nose[13..13]), (! nose[12..12]), (! nose[11..11]), (! nose[10..10]), (! nose[9..9]), (! nose[8..8]), (! nose[7..7]), (! nose[6..6]), (! nose[5..5]), (! nose[4..4]), (! nose[3..3]), (! nose[2..2]), ((! nose[1..1]) # sel[1..1]), ((! nose[0..0]) # sel[0..0]));
	StageIn[] = StageIn_tmp[];
	StageIn_tmp[] = ( StageOut[41..0], B"000");
	StageOut[] = ( ((( StageIn[40..39], NumeratorIn[182..182]) & selnose[195..195]) # (prestg[41..39] & (! selnose[195..195]))), ((( StageIn[37..36], NumeratorIn[169..169]) & selnose[180..180]) # (prestg[38..36] & (! selnose[180..180]))), ((( StageIn[34..33], NumeratorIn[156..156]) & selnose[165..165]) # (prestg[35..33] & (! selnose[165..165]))), ((( StageIn[31..30], NumeratorIn[143..143]) & selnose[150..150]) # (prestg[32..30] & (! selnose[150..150]))), ((( StageIn[28..27], NumeratorIn[130..130]) & selnose[135..135]) # (prestg[29..27] & (! selnose[135..135]))), ((( StageIn[25..24], NumeratorIn[117..117]) & selnose[120..120]) # (prestg[26..24] & (! selnose[120..120]))), ((( StageIn[22..21], NumeratorIn[104..104]) & selnose[105..105]) # (prestg[23..21] & (! selnose[105..105]))), ((( StageIn[19..18], NumeratorIn[91..91]) & selnose[90..90]) # (prestg[20..18] & (! selnose[90..90]))), ((( StageIn[16..15], NumeratorIn[78..78]) & selnose[75..75]) # (prestg[17..15] & (! selnose[75..75]))), ((( StageIn[13..12], NumeratorIn[65..65]) & selnose[60..60]) # (prestg[14..12] & (! selnose[60..60]))), ((( StageIn[10..9], NumeratorIn[52..52]) & selnose[45..45]) # (prestg[11..9] & (! selnose[45..45]))), ((( StageIn[7..6], NumeratorIn[39..39]) & selnose[30..30]) # (prestg[8..6] & (! selnose[30..30]))), ((( StageIn[4..3], NumeratorIn[26..26]) & selnose[15..15]) # (prestg[5..3] & (! selnose[15..15]))), ((( StageIn[1..0], NumeratorIn[13..13]) & selnose[0..0]) # (prestg[2..0] & (! selnose[0..0]))));
END;
--VALID FILE
