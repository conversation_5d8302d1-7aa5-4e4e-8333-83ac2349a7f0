/*
 * 超简化版本 - 最容易编译通过
 * 只包含核心SPI发送功能
 */

#include "ti_msp_dl_config.h"

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 延时
    DL_Common_delayCycles(1000000);
    
    while(1) {
        // 测试数据1：档位1(10KHz) + 75%幅度
        uint16_t data1 = 0x1C00;
        
        // CS拉低 (假设PA4)
        DL_GPIO_clearPins(GPIOA, DL_GPIO_PIN_4);
        
        // 发送SPI数据
        DL_SPI_transmitData16(SPI_0_INST, data1);
        while(DL_SPI_isBusy(SPI_0_INST));
        
        // CS拉高
        DL_GPIO_setPins(GPIOA, DL_GPIO_PIN_4);
        
        // 延时2秒
        DL_Common_delayCycles(64000000);
        
        // 测试数据2：档位2(100KHz) + 50%幅度
        uint16_t data2 = 0x2800;
        
        DL_GPIO_clearPins(GPIOA, DL_GPIO_PIN_4);
        DL_SPI_transmitData16(SPI_0_INST, data2);
        while(DL_SPI_isBusy(SPI_0_INST));
        DL_GPIO_setPins(GPIOA, DL_GPIO_PIN_4);
        
        DL_Common_delayCycles(64000000);
        
        // 测试数据3：档位0(1KHz) + 100%幅度
        uint16_t data3 = 0x0FFF;
        
        DL_GPIO_clearPins(GPIOA, DL_GPIO_PIN_4);
        DL_SPI_transmitData16(SPI_0_INST, data3);
        while(DL_SPI_isBusy(SPI_0_INST));
        DL_GPIO_setPins(GPIOA, DL_GPIO_PIN_4);
        
        DL_Common_delayCycles(64000000);
    }
}
