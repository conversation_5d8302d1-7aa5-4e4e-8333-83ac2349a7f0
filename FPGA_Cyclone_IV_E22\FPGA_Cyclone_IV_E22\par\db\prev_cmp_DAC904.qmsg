{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1754014307648 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1754014307649 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 10:11:47 2025 " "Processing started: Fri Aug 01 10:11:47 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1754014307649 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1754014307649 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1754014307649 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1754014307768 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Tri " "Found entity 1: ROM_Tri" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307795 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307795 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v" { { "Info" "ISGN_ENTITY_NAME" "1 sel_wave " "Found entity 1: sel_wave" {  } { { "../rtl/sel_wave.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/sel_wave.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307798 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307798 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_delay " "Found entity 1: key_delay" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307799 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307799 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_con " "Found entity 1: key_con" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_con.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307800 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307800 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v" { { "Info" "ISGN_ENTITY_NAME" "1 add_32bit " "Found entity 1: add_32bit" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307802 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307802 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Sin " "Found entity 1: ROM_Sin" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307803 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307803 ""}
{ "Error" "EVRFX_VERI_SYNTAX_ERROR" "\"wire\";  expecting \"end\" DAC904_TOP.v(74) " "Verilog HDL syntax error at DAC904_TOP.v(74) near text \"wire\";  expecting \"end\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 74 0 0 } }  } 0 10170 "Verilog HDL syntax error at %2!s! near text %1!s!" 0 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Error" "EVRFX_VERI_SYNTAX_ERROR" "\"wire\";  expecting \"end\" DAC904_TOP.v(75) " "Verilog HDL syntax error at DAC904_TOP.v(75) near text \"wire\";  expecting \"end\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 75 0 0 } }  } 0 10170 "Verilog HDL syntax error at %2!s! near text %1!s!" 0 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Error" "EVRFX_VERI_SYNTAX_ERROR" "\"wire\";  expecting \"end\" DAC904_TOP.v(76) " "Verilog HDL syntax error at DAC904_TOP.v(76) near text \"wire\";  expecting \"end\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 76 0 0 } }  } 0 10170 "Verilog HDL syntax error at %2!s! near text %1!s!" 0 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Warning" "WVRFX_L3_VERI_MIXED_BLOCKING_NONBLOCKING_ASSIGNMENT" "DAC904_TOP.v(59) " "Verilog HDL information at DAC904_TOP.v(59): always construct contains both blocking and non-blocking assignments" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 59 0 0 } }  } 0 10268 "Verilog HDL information at %1!s!: always construct contains both blocking and non-blocking assignments" 1 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Error" "EVRFX_VERI_DESIGN_UNIT_IGNORED" "DAC904_TOP DAC904_TOP.v(1) " "Ignored design unit \"DAC904_TOP\" at DAC904_TOP.v(1) due to previous errors" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 1 0 0 } }  } 0 10112 "Ignored design unit \"%1!s!\" at %2!s! due to previous errors" 0 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v 0 0 " "Found 0 design units, including 0 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v" {  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307804 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK " "Found entity 1: PLL_CLK" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014307807 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014307807 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/output_files/DAC904.map.smsg " "Generated suppressed messages file C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/output_files/DAC904.map.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Quartus II" 0 -1 1754014307818 ""}
{ "Error" "EQEXE_ERROR_COUNT" "Analysis & Synthesis 4 s 1  Quartus II 64-Bit " "Quartus II 64-Bit Analysis & Synthesis was unsuccessful. 4 errors, 1 warning" { { "Error" "EQEXE_END_PEAK_VSIZE_MEMORY" "4609 " "Peak virtual memory: 4609 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1754014307839 ""} { "Error" "EQEXE_END_BANNER_TIME" "Fri Aug 01 10:11:47 2025 " "Processing ended: Fri Aug 01 10:11:47 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1754014307839 ""} { "Error" "EQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1754014307839 ""} { "Error" "EQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1754014307839 ""}  } {  } 0 0 "%6!s! %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1754014307839 ""}
{ "Error" "EFLOW_ERROR_COUNT" "Full Compilation 6 s 1  " "Quartus II Full Compilation was unsuccessful. 6 errors, 1 warning" {  } {  } 0 293001 "Quartus II %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1754014308420 ""}
