{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1754014448567 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1754014448567 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 10:14:08 2025 " "Processing started: Fri Aug 01 10:14:08 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1754014448567 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1754014448567 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1754014448567 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1754014448688 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Tri " "Found entity 1: ROM_Tri" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448712 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448712 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v" { { "Info" "ISGN_ENTITY_NAME" "1 sel_wave " "Found entity 1: sel_wave" {  } { { "../rtl/sel_wave.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/sel_wave.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448713 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448713 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_delay " "Found entity 1: key_delay" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448714 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448714 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_con " "Found entity 1: key_con" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_con.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448715 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448715 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v" { { "Info" "ISGN_ENTITY_NAME" "1 add_32bit " "Found entity 1: add_32bit" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448716 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448716 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Sin " "Found entity 1: ROM_Sin" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448717 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448717 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_TOP " "Found entity 1: DAC904_TOP" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448718 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448718 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK " "Found entity 1: PLL_CLK" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014448720 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014448720 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "spi_data_temp DAC904_TOP.v(67) " "Verilog HDL error at DAC904_TOP.v(67): object \"spi_data_temp\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 67 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448720 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "data_valid DAC904_TOP.v(70) " "Verilog HDL error at DAC904_TOP.v(70): object \"data_valid\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 70 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448720 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "data_valid DAC904_TOP.v(80) " "Verilog HDL error at DAC904_TOP.v(80): object \"data_valid\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 80 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448721 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "spi_data_temp DAC904_TOP.v(84) " "Verilog HDL error at DAC904_TOP.v(84): object \"spi_data_temp\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 84 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448721 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "spi_data_temp DAC904_TOP.v(88) " "Verilog HDL error at DAC904_TOP.v(88): object \"spi_data_temp\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 88 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448721 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "data_valid DAC904_TOP.v(89) " "Verilog HDL error at DAC904_TOP.v(89): object \"data_valid\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 89 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448721 ""}
{ "Error" "EVRFX_VERI_UNDECLARED_OBJECT" "data_valid DAC904_TOP.v(94) " "Verilog HDL error at DAC904_TOP.v(94): object \"data_valid\" is not declared" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 94 0 0 } }  } 0 10161 "Verilog HDL error at %2!s!: object \"%1!s!\" is not declared" 0 0 "Quartus II" 0 -1 1754014448721 ""}
{ "Error" "EQEXE_ERROR_COUNT" "Analysis & Synthesis 7 s 1  Quartus II 64-Bit " "Quartus II 64-Bit Analysis & Synthesis was unsuccessful. 7 errors, 1 warning" { { "Error" "EQEXE_END_PEAK_VSIZE_MEMORY" "4608 " "Peak virtual memory: 4608 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1754014448747 ""} { "Error" "EQEXE_END_BANNER_TIME" "Fri Aug 01 10:14:08 2025 " "Processing ended: Fri Aug 01 10:14:08 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1754014448747 ""} { "Error" "EQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1754014448747 ""} { "Error" "EQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1754014448747 ""}  } {  } 0 0 "%6!s! %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1754014448747 ""}
{ "Error" "EFLOW_ERROR_COUNT" "Full Compilation 9 s 1  " "Quartus II Full Compilation was unsuccessful. 9 errors, 1 warning" {  } {  } 0 293001 "Quartus II %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1754014449296 ""}
