// Copyright (C) 1991-2013 Altera Corporation
// Your use of Altera Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Altera Program License 
// Subscription Agreement, Altera MegaCore Function License 
// Agreement, or other applicable license agreement, including, 
// without limitation, that your use is for the sole purpose of 
// programming logic devices manufactured by Altera and sold by 
// Altera or its authorized distributors.  Please refer to the 
// applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Fast Corner delays for the design using part EP4CE6E22C8,
// with speed grade M, core voltage 1.2V, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (Verilog) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "DAC904_TOP")
  (DATE "08/01/2025 07:25:27")
  (VENDOR "Altera")
  (PROGRAM "Quartus II 64-Bit")
  (VERSION "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_CLK\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (602:602:602) (644:644:644))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[0\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (692:692:692) (773:773:773))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[1\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (752:752:752) (828:828:828))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[2\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (749:749:749) (817:817:817))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[3\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (566:566:566) (631:631:631))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[4\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (769:769:769) (857:857:857))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[5\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (730:730:730) (798:798:798))
        (IOPATH i o (2496:2496:2496) (2557:2557:2557))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[6\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (523:523:523) (574:574:574))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[7\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (523:523:523) (573:573:573))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[8\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (486:486:486) (526:526:526))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[9\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (503:503:503) (547:547:547))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[10\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (672:672:672) (742:742:742))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[11\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (855:855:855) (954:954:954))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[12\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (749:749:749) (843:843:843))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE DAC_DATA\[13\]\~output)
    (DELAY
      (ABSOLUTE
        (PORT i (930:930:930) (842:842:842))
        (IOPATH i o (1570:1570:1570) (1565:1565:1565))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_RST\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE SYS_RST\~inputclkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (108:108:108) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE SYS_CLK\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_pll")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|pll1)
    (DELAY
      (ABSOLUTE
        (PORT areset (610:610:610) (610:610:610))
        (PORT inclk[0] (1098:1098:1098) (1098:1098:1098))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE u_PLL_CLK\|altpll_component\|auto_generated\|wire_pll1_clk\[0\]\~clkctrl)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (1121:1121:1121) (1119:1119:1119))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_mosi\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (765:765:765) (676:676:676))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_sclk\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE sclk_d)
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT asdata (947:947:947) (855:855:855))
        (PORT clrn (749:749:749) (721:721:721))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE spi_cs_n\~input)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE always1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (794:794:794) (695:695:695))
        (PORT datad (1799:1799:1799) (2012:2012:2012))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (194:194:194) (245:245:245))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT asdata (370:370:370) (423:423:423))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT asdata (306:306:306) (345:345:345))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT asdata (320:320:320) (360:360:360))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[5\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (132:132:132) (169:169:169))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (131:131:131) (169:169:169))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (193:193:193) (243:243:243))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (131:131:131) (168:168:168))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (143:143:143) (180:180:180))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[10\]\~0)
    (DELAY
      (ABSOLUTE
        (PORT datab (156:156:156) (204:204:204))
        (IOPATH datab combout (160:160:160) (156:156:156))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (744:744:744) (765:765:765))
        (PORT asdata (306:306:306) (345:345:345))
        (PORT clrn (749:749:749) (721:721:721))
        (PORT ena (485:485:485) (513:513:513))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT asdata (868:868:868) (992:992:992))
        (PORT clrn (764:764:764) (735:735:735))
        (PORT ena (1012:1012:1012) (1119:1119:1119))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE spi_data\[13\]\~1)
    (DELAY
      (ABSOLUTE
        (PORT datac (199:199:199) (272:272:272))
        (IOPATH datac combout (120:120:120) (125:125:125))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (764:764:764) (735:735:735))
        (PORT ena (1012:1012:1012) (1119:1119:1119))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT asdata (388:388:388) (437:437:437))
        (PORT clrn (764:764:764) (735:735:735))
        (PORT ena (1012:1012:1012) (1119:1119:1119))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE spi_data\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (772:772:772))
        (PORT asdata (383:383:383) (429:429:429))
        (PORT clrn (764:764:764) (735:735:735))
        (PORT ena (1012:1012:1012) (1119:1119:1119))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr2\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (207:207:207) (286:286:286))
        (PORT datab (207:207:207) (282:282:282))
        (PORT datac (196:196:196) (268:268:268))
        (PORT datad (257:257:257) (319:319:319))
        (IOPATH dataa combout (166:166:166) (173:173:173))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr3\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (422:422:422) (525:525:525))
        (PORT datab (424:424:424) (515:515:515))
        (PORT datac (238:238:238) (300:300:300))
        (PORT datad (387:387:387) (467:467:467))
        (IOPATH dataa combout (195:195:195) (193:193:193))
        (IOPATH datab combout (196:196:196) (192:192:192))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr8\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (418:418:418) (520:520:520))
        (PORT datab (422:422:422) (513:513:513))
        (PORT datac (246:246:246) (308:308:308))
        (PORT datad (382:382:382) (461:461:461))
        (IOPATH dataa combout (165:165:165) (159:159:159))
        (IOPATH datab combout (192:192:192) (181:181:181))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr13\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (206:206:206) (283:283:283))
        (PORT datab (209:209:209) (285:285:285))
        (PORT datac (199:199:199) (271:271:271))
        (PORT datad (260:260:260) (322:322:322))
        (IOPATH dataa combout (181:181:181) (193:193:193))
        (IOPATH datab combout (182:182:182) (193:193:193))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr14\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (209:209:209) (288:288:288))
        (PORT datab (203:203:203) (278:278:278))
        (PORT datac (191:191:191) (263:263:263))
        (PORT datad (254:254:254) (315:315:315))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH datab combout (182:182:182) (193:193:193))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr15\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (205:205:205) (283:283:283))
        (PORT datab (212:212:212) (287:287:287))
        (PORT datac (202:202:202) (274:274:274))
        (PORT datad (264:264:264) (326:326:326))
        (IOPATH dataa combout (188:188:188) (179:179:179))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr16\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (210:210:210) (289:289:289))
        (PORT datab (199:199:199) (273:273:273))
        (PORT datac (186:186:186) (257:257:257))
        (PORT datad (251:251:251) (312:312:312))
        (IOPATH dataa combout (188:188:188) (179:179:179))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr17\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (209:209:209) (288:288:288))
        (PORT datab (202:202:202) (276:276:276))
        (PORT datac (189:189:189) (261:261:261))
        (PORT datad (253:253:253) (314:314:314))
        (IOPATH dataa combout (188:188:188) (203:203:203))
        (IOPATH datab combout (190:190:190) (205:205:205))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr19\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (205:205:205) (283:283:283))
        (PORT datab (211:211:211) (287:287:287))
        (PORT datac (201:201:201) (273:273:273))
        (PORT datad (263:263:263) (325:325:325))
        (IOPATH dataa combout (195:195:195) (193:193:193))
        (IOPATH datab combout (196:196:196) (192:192:192))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr20\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (206:206:206) (283:283:283))
        (PORT datab (210:210:210) (286:286:286))
        (PORT datac (199:199:199) (272:272:272))
        (PORT datad (261:261:261) (323:323:323))
        (IOPATH dataa combout (166:166:166) (173:173:173))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr23\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (208:208:208) (287:287:287))
        (PORT datab (204:204:204) (279:279:279))
        (PORT datac (193:193:193) (264:264:264))
        (PORT datad (255:255:255) (316:316:316))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr24\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (206:206:206) (283:283:283))
        (PORT datab (210:210:210) (286:286:286))
        (PORT datac (200:200:200) (272:272:272))
        (PORT datad (262:262:262) (324:324:324))
        (IOPATH dataa combout (188:188:188) (179:179:179))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr5\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (206:206:206) (282:282:282))
        (PORT datab (208:208:208) (284:284:284))
        (PORT datac (197:197:197) (270:270:270))
        (PORT datad (258:258:258) (320:320:320))
        (IOPATH dataa combout (188:188:188) (203:203:203))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr26\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (209:209:209) (288:288:288))
        (PORT datab (200:200:200) (274:274:274))
        (PORT datac (187:187:187) (259:259:259))
        (PORT datad (252:252:252) (313:313:313))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH datab combout (182:182:182) (177:177:177))
        (IOPATH datac combout (120:120:120) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr28\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (280:280:280))
        (PORT datab (224:224:224) (282:282:282))
        (PORT datad (249:249:249) (310:310:310))
        (IOPATH dataa combout (181:181:181) (193:193:193))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr9\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (141:141:141) (192:192:192))
        (PORT datab (194:194:194) (268:268:268))
        (PORT datad (197:197:197) (260:260:260))
        (IOPATH dataa combout (159:159:159) (173:173:173))
        (IOPATH datab combout (188:188:188) (193:193:193))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr30\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (202:202:202) (283:283:283))
        (PORT datab (197:197:197) (271:271:271))
        (PORT datad (195:195:195) (259:259:259))
        (IOPATH dataa combout (166:166:166) (157:157:157))
        (IOPATH datab combout (190:190:190) (205:205:205))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[0\]\~32)
    (DELAY
      (ABSOLUTE
        (PORT dataa (742:742:742) (846:846:846))
        (PORT datab (128:128:128) (176:176:176))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[1\]\~34)
    (DELAY
      (ABSOLUTE
        (PORT dataa (478:478:478) (560:560:560))
        (PORT datab (128:128:128) (176:176:176))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[2\]\~36)
    (DELAY
      (ABSOLUTE
        (PORT dataa (645:645:645) (750:750:750))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[3\]\~38)
    (DELAY
      (ABSOLUTE
        (PORT dataa (370:370:370) (442:442:442))
        (PORT datab (200:200:200) (254:254:254))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[4\]\~40)
    (DELAY
      (ABSOLUTE
        (PORT dataa (698:698:698) (815:815:815))
        (PORT datab (130:130:130) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[5\]\~42)
    (DELAY
      (ABSOLUTE
        (PORT dataa (645:645:645) (751:751:751))
        (PORT datab (200:200:200) (254:254:254))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[6\]\~44)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (180:180:180))
        (PORT datab (337:337:337) (400:400:400))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[7\]\~46)
    (DELAY
      (ABSOLUTE
        (PORT dataa (371:371:371) (443:443:443))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[8\]\~48)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (257:257:257))
        (PORT datab (362:362:362) (436:436:436))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[9\]\~50)
    (DELAY
      (ABSOLUTE
        (PORT dataa (200:200:200) (261:261:261))
        (PORT datab (354:354:354) (418:418:418))
        (IOPATH dataa combout (166:166:166) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[10\]\~52)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (420:420:420))
        (PORT datab (130:130:130) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[11\]\~54)
    (DELAY
      (ABSOLUTE
        (PORT dataa (131:131:131) (180:180:180))
        (PORT datab (346:346:346) (410:410:410))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[12\]\~56)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (258:258:258))
        (PORT datab (362:362:362) (436:436:436))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[13\]\~58)
    (DELAY
      (ABSOLUTE
        (PORT dataa (130:130:130) (181:181:181))
        (PORT datab (346:346:346) (404:404:404))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[14\]\~60)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (427:427:427))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[14\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[15\]\~62)
    (DELAY
      (ABSOLUTE
        (PORT dataa (129:129:129) (180:180:180))
        (PORT datab (476:476:476) (559:559:559))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[15\])
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (727:727:727) (818:818:818))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[16\]\~64)
    (DELAY
      (ABSOLUTE
        (PORT dataa (220:220:220) (276:276:276))
        (PORT datab (354:354:354) (421:421:421))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[16\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[17\]\~66)
    (DELAY
      (ABSOLUTE
        (PORT dataa (345:345:345) (414:414:414))
        (PORT datab (129:129:129) (177:177:177))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (167:167:167) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[17\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[18\]\~68)
    (DELAY
      (ABSOLUTE
        (PORT dataa (360:360:360) (428:428:428))
        (PORT datab (130:130:130) (178:178:178))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[18\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[19\]\~70)
    (DELAY
      (ABSOLUTE
        (PORT dataa (131:131:131) (181:181:181))
        (PORT datab (377:377:377) (447:447:447))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[19\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[20\]\~72)
    (DELAY
      (ABSOLUTE
        (PORT dataa (209:209:209) (273:273:273))
        (PORT datab (371:371:371) (439:439:439))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[20\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[21\]\~74)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (193:193:193))
        (PORT datab (607:607:607) (697:697:697))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[21\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[22\]\~76)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (426:426:426))
        (PORT datab (208:208:208) (270:270:270))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[22\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[23\]\~78)
    (DELAY
      (ABSOLUTE
        (PORT dataa (210:210:210) (272:272:272))
        (PORT datab (376:376:376) (446:446:446))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[23\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr6\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (423:423:423) (527:527:527))
        (PORT datab (424:424:424) (516:516:516))
        (PORT datac (237:237:237) (298:298:298))
        (PORT datad (388:388:388) (468:468:468))
        (IOPATH dataa combout (195:195:195) (193:193:193))
        (IOPATH datab combout (188:188:188) (177:177:177))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[24\]\~80)
    (DELAY
      (ABSOLUTE
        (PORT dataa (214:214:214) (277:277:277))
        (PORT datab (328:328:328) (389:389:389))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[24\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[25\]\~82)
    (DELAY
      (ABSOLUTE
        (PORT dataa (549:549:549) (661:661:661))
        (PORT datab (378:378:378) (450:450:450))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[25\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr4\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (419:419:419) (522:522:522))
        (PORT datab (422:422:422) (514:514:514))
        (PORT datac (245:245:245) (307:307:307))
        (PORT datad (383:383:383) (462:462:462))
        (IOPATH dataa combout (165:165:165) (159:159:159))
        (IOPATH datab combout (192:192:192) (181:181:181))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[26\]\~84)
    (DELAY
      (ABSOLUTE
        (PORT dataa (208:208:208) (267:267:267))
        (PORT datab (348:348:348) (418:418:418))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[26\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[27\]\~86)
    (DELAY
      (ABSOLUTE
        (PORT dataa (155:155:155) (205:205:205))
        (PORT datab (375:375:375) (445:445:445))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[27\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[28\]\~88)
    (DELAY
      (ABSOLUTE
        (PORT dataa (225:225:225) (286:286:286))
        (PORT datab (372:372:372) (440:440:440))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[28\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (419:419:419) (521:521:521))
        (PORT datab (422:422:422) (514:514:514))
        (PORT datac (246:246:246) (308:308:308))
        (PORT datad (383:383:383) (462:462:462))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[29\]\~90)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (194:194:194))
        (PORT datab (356:356:356) (420:420:420))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[29\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE WideOr0\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (420:420:420) (523:523:523))
        (PORT datab (423:423:423) (514:514:514))
        (PORT datac (243:243:243) (305:305:305))
        (PORT datad (384:384:384) (464:464:464))
        (IOPATH dataa combout (166:166:166) (163:163:163))
        (IOPATH datab combout (166:166:166) (158:158:158))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[30\]\~92)
    (DELAY
      (ABSOLUTE
        (PORT dataa (368:368:368) (434:434:434))
        (PORT datab (154:154:154) (201:201:201))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[30\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_add_32bit\|add\[31\]\~94)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (IOPATH dataa combout (195:195:195) (203:203:203))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_add_32bit\|add\[31\])
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (779:779:779))
        (PORT d (37:37:37) (50:50:50))
        (PORT sclr (733:733:733) (820:820:820))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD sclr (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (564:564:564) (674:674:674))
        (PORT d[1] (586:586:586) (702:702:702))
        (PORT d[2] (568:568:568) (677:677:677))
        (PORT d[3] (568:568:568) (685:685:685))
        (PORT d[4] (425:425:425) (519:519:519))
        (PORT d[5] (405:405:405) (490:490:490))
        (PORT d[6] (1272:1272:1272) (1481:1481:1481))
        (PORT d[7] (461:461:461) (556:556:556))
        (PORT d[8] (437:437:437) (529:529:529))
        (PORT d[9] (738:738:738) (868:868:868))
        (PORT d[10] (436:436:436) (521:521:521))
        (PORT d[11] (681:681:681) (794:794:794))
        (PORT clk (940:940:940) (972:972:972))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (940:940:940) (972:972:972))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (973:973:973))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (922:922:922) (953:953:953))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (462:462:462) (485:485:485))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a0.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[0\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (545:545:545) (618:618:618))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[0\])
    (DELAY
      (ABSOLUTE
        (PORT clk (755:755:755) (775:775:775))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (767:767:767) (738:738:738))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[1\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (533:533:533) (608:608:608))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[1\])
    (DELAY
      (ABSOLUTE
        (PORT clk (755:755:755) (775:775:775))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (767:767:767) (738:738:738))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (571:571:571) (685:685:685))
        (PORT d[1] (749:749:749) (875:875:875))
        (PORT d[2] (556:556:556) (655:655:655))
        (PORT d[3] (742:742:742) (876:876:876))
        (PORT d[4] (398:398:398) (482:482:482))
        (PORT d[5] (411:411:411) (501:501:501))
        (PORT d[6] (1440:1440:1440) (1668:1668:1668))
        (PORT d[7] (417:417:417) (497:497:497))
        (PORT d[8] (409:409:409) (488:488:488))
        (PORT d[9] (707:707:707) (828:828:828))
        (PORT d[10] (423:423:423) (504:504:504))
        (PORT d[11] (540:540:540) (633:633:633))
        (PORT clk (941:941:941) (973:973:973))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (973:973:973))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (942:942:942) (974:974:974))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (923:923:923) (954:954:954))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (486:486:486))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a2.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (487:487:487))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[2\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (552:552:552) (633:633:633))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[2\])
    (DELAY
      (ABSOLUTE
        (PORT clk (746:746:746) (766:766:766))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (751:751:751) (722:722:722))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[3\])
    (DELAY
      (ABSOLUTE
        (PORT clk (746:746:746) (766:766:766))
        (PORT asdata (819:819:819) (896:896:896))
        (PORT clrn (751:751:751) (722:722:722))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (721:721:721) (844:844:844))
        (PORT d[1] (755:755:755) (893:893:893))
        (PORT d[2] (742:742:742) (876:876:876))
        (PORT d[3] (594:594:594) (717:717:717))
        (PORT d[4] (420:420:420) (508:508:508))
        (PORT d[5] (424:424:424) (515:515:515))
        (PORT d[6] (1229:1229:1229) (1425:1425:1425))
        (PORT d[7] (459:459:459) (552:552:552))
        (PORT d[8] (445:445:445) (538:538:538))
        (PORT d[9] (594:594:594) (706:706:706))
        (PORT d[10] (458:458:458) (551:551:551))
        (PORT d[11] (554:554:554) (650:650:650))
        (PORT clk (944:944:944) (977:977:977))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (944:944:944) (977:977:977))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (945:945:945) (978:978:978))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (926:926:926) (958:958:958))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (466:466:466) (490:490:490))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (467:467:467) (491:491:491))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (467:467:467) (491:491:491))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a4.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (467:467:467) (491:491:491))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[4\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (672:672:672) (760:760:760))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[4\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (763:763:763))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (755:755:755) (725:725:725))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[5\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (763:763:763))
        (PORT asdata (843:843:843) (929:929:929))
        (PORT clrn (755:755:755) (725:725:725))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD asdata (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (559:559:559) (664:664:664))
        (PORT d[1] (594:594:594) (705:705:705))
        (PORT d[2] (558:558:558) (662:662:662))
        (PORT d[3] (582:582:582) (695:695:695))
        (PORT d[4] (419:419:419) (508:508:508))
        (PORT d[5] (572:572:572) (678:678:678))
        (PORT d[6] (1255:1255:1255) (1459:1459:1459))
        (PORT d[7] (778:778:778) (909:909:909))
        (PORT d[8] (431:431:431) (518:518:518))
        (PORT d[9] (581:581:581) (688:688:688))
        (PORT d[10] (467:467:467) (563:563:563))
        (PORT d[11] (547:547:547) (642:642:642))
        (PORT clk (945:945:945) (979:979:979))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (945:945:945) (979:979:979))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (946:946:946) (980:980:980))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (927:927:927) (960:960:960))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (467:467:467) (492:492:492))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (468:468:468) (493:493:493))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (468:468:468) (493:493:493))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a6.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (468:468:468) (493:493:493))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[6\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (567:567:567) (646:646:646))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[6\])
    (DELAY
      (ABSOLUTE
        (PORT clk (739:739:739) (759:759:759))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (751:751:751) (722:722:722))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[7\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (413:413:413) (480:480:480))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[7\])
    (DELAY
      (ABSOLUTE
        (PORT clk (742:742:742) (762:762:762))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (747:747:747) (719:719:719))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (731:731:731) (856:856:856))
        (PORT d[1] (725:725:725) (847:847:847))
        (PORT d[2] (739:739:739) (871:871:871))
        (PORT d[3] (735:735:735) (871:871:871))
        (PORT d[4] (602:602:602) (716:716:716))
        (PORT d[5] (577:577:577) (682:682:682))
        (PORT d[6] (1063:1063:1063) (1234:1234:1234))
        (PORT d[7] (621:621:621) (732:732:732))
        (PORT d[8] (616:616:616) (730:730:730))
        (PORT d[9] (742:742:742) (864:864:864))
        (PORT d[10] (639:639:639) (758:758:758))
        (PORT d[11] (578:578:578) (684:684:684))
        (PORT clk (946:946:946) (979:979:979))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (946:946:946) (979:979:979))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (947:947:947) (980:980:980))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (928:928:928) (960:960:960))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (468:468:468) (492:492:492))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (469:469:469) (493:493:493))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (469:469:469) (493:493:493))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a8.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (469:469:469) (493:493:493))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[8\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (548:548:548) (633:633:633))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[8\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (761:761:761) (732:732:732))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[9\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (560:560:560) (645:645:645))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[9\])
    (DELAY
      (ABSOLUTE
        (PORT clk (749:749:749) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (761:761:761) (732:732:732))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (739:739:739) (872:872:872))
        (PORT d[1] (415:415:415) (500:500:500))
        (PORT d[2] (399:399:399) (473:473:473))
        (PORT d[3] (752:752:752) (888:888:888))
        (PORT d[4] (572:572:572) (680:680:680))
        (PORT d[5] (571:571:571) (675:675:675))
        (PORT d[6] (602:602:602) (717:717:717))
        (PORT d[7] (411:411:411) (486:486:486))
        (PORT d[8] (408:408:408) (486:486:486))
        (PORT d[9] (707:707:707) (827:827:827))
        (PORT d[10] (401:401:401) (474:474:474))
        (PORT d[11] (540:540:540) (637:637:637))
        (PORT clk (948:948:948) (981:981:981))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (948:948:948) (981:981:981))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (949:949:949) (982:982:982))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (930:930:930) (962:962:962))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (470:470:470) (494:494:494))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (471:471:471) (495:495:495))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (471:471:471) (495:495:495))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a10.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (471:471:471) (495:495:495))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[10\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (684:684:684) (775:775:775))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[10\])
    (DELAY
      (ABSOLUTE
        (PORT clk (746:746:746) (767:767:767))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (759:759:759) (729:729:729))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[11\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (680:680:680) (795:795:795))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[11\])
    (DELAY
      (ABSOLUTE
        (PORT clk (746:746:746) (767:767:767))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (759:759:759) (729:729:729))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.addr_a_register)
    (DELAY
      (ABSOLUTE
        (PORT d[0] (573:573:573) (686:686:686))
        (PORT d[1] (590:590:590) (697:697:697))
        (PORT d[2] (583:583:583) (688:688:688))
        (PORT d[3] (561:561:561) (670:670:670))
        (PORT d[4] (414:414:414) (498:498:498))
        (PORT d[5] (414:414:414) (499:499:499))
        (PORT d[6] (1455:1455:1455) (1686:1686:1686))
        (PORT d[7] (445:445:445) (532:532:532))
        (PORT d[8] (423:423:423) (506:506:506))
        (PORT d[9] (571:571:571) (676:676:676))
        (PORT d[10] (464:464:464) (557:557:557))
        (PORT d[11] (548:548:548) (642:642:642))
        (PORT clk (941:941:941) (974:974:974))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (104:104:104))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (941:941:941) (974:974:974))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_a)
    (DELAY
      (ABSOLUTE
        (PORT clk (942:942:942) (975:975:975))
        (IOPATH (posedge clk) pulse (0:0:0) (1104:1104:1104))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.dataout_a_register)
    (DELAY
      (ABSOLUTE
        (PORT clk (923:923:923) (955:955:955))
        (IOPATH (posedge clk) q (164:164:164) (167:167:167))
      )
    )
    (TIMINGCHECK
      (SETUP d (posedge clk) (25:25:25))
      (HOLD d (posedge clk) (90:90:90))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_register")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.active_core_port_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (463:463:463) (487:487:487))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.ftpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_ram_pulse_generator")
    (INSTANCE u_ROM_Sin\|altsyncram_component\|auto_generated\|ram_block1a12.rwpgen_b)
    (DELAY
      (ABSOLUTE
        (PORT clk (464:464:464) (488:488:488))
        (IOPATH (posedge clk) pulse (0:0:0) (1222:1222:1222))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[12\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (845:845:845) (954:954:954))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[12\])
    (DELAY
      (ABSOLUTE
        (PORT clk (748:748:748) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE u_sel_wave\|da_out_reg\[13\]\~feeder)
    (DELAY
      (ABSOLUTE
        (PORT datad (856:856:856) (972:972:972))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE u_sel_wave\|da_out_reg\[13\])
    (DELAY
      (ABSOLUTE
        (PORT clk (748:748:748) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (PORT clrn (753:753:753) (724:724:724))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
        (IOPATH (negedge clrn) q (110:110:110) (110:110:110))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult1.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[0] (351:351:351) (408:408:408))
        (PORT dataa[1] (351:351:351) (407:407:407))
        (PORT dataa[2] (344:344:344) (403:403:403))
        (PORT dataa[3] (366:366:366) (433:433:433))
        (PORT dataa[4] (346:346:346) (406:406:406))
        (PORT dataa[5] (339:339:339) (400:400:400))
        (PORT dataa[6] (347:347:347) (410:410:410))
        (PORT dataa[7] (411:411:411) (482:482:482))
        (PORT dataa[8] (506:506:506) (586:586:586))
        (PORT dataa[9] (524:524:524) (608:608:608))
        (PORT dataa[10] (190:190:190) (229:229:229))
        (PORT dataa[11] (189:189:189) (228:228:228))
        (PORT dataa[12] (353:353:353) (418:418:418))
        (PORT dataa[13] (554:554:554) (480:480:480))
        (PORT dataa[14] (440:440:440) (376:376:376))
        (PORT dataa[15] (445:445:445) (378:378:378))
        (PORT dataa[16] (554:554:554) (480:480:480))
        (PORT dataa[17] (440:440:440) (376:376:376))
        (PORT datab[6] (343:343:343) (405:405:405))
        (PORT datab[7] (442:442:442) (513:513:513))
        (PORT datab[8] (357:357:357) (417:417:417))
        (PORT datab[9] (481:481:481) (553:553:553))
        (PORT datab[10] (355:355:355) (417:417:417))
        (PORT datab[11] (349:349:349) (399:399:399))
        (PORT datab[12] (355:355:355) (415:415:415))
        (PORT datab[13] (358:358:358) (419:419:419))
        (PORT datab[14] (370:370:370) (432:432:432))
        (PORT datab[15] (338:338:338) (379:379:379))
        (PORT datab[16] (420:420:420) (361:361:361))
        (PORT datab[17] (506:506:506) (438:438:438))
        (IOPATH dataa dataout (1719:1719:1719) (1719:1719:1719))
        (IOPATH datab dataout (1676:1676:1676) (1676:1676:1676))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out2)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (61:61:61) (64:64:64))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_mult_internal")
    (INSTANCE Mult0\|auto_generated\|mac_mult3.mac_multiply)
    (DELAY
      (ABSOLUTE
        (PORT dataa[6] (198:198:198) (243:243:243))
        (PORT dataa[7] (314:314:314) (359:359:359))
        (PORT dataa[8] (203:203:203) (242:242:242))
        (PORT dataa[9] (211:211:211) (248:248:248))
        (PORT dataa[10] (304:304:304) (351:351:351))
        (PORT dataa[11] (214:214:214) (256:256:256))
        (PORT dataa[12] (198:198:198) (242:242:242))
        (PORT dataa[13] (213:213:213) (255:255:255))
        (PORT dataa[14] (317:317:317) (361:361:361))
        (PORT dataa[15] (211:211:211) (247:247:247))
        (PORT dataa[16] (245:245:245) (205:205:205))
        (PORT dataa[17] (361:361:361) (312:312:312))
        (PORT datab[10] (410:410:410) (352:352:352))
        (PORT datab[11] (295:295:295) (245:245:245))
        (PORT datab[12] (299:299:299) (246:246:246))
        (PORT datab[13] (410:410:410) (352:352:352))
        (PORT datab[14] (295:295:295) (245:245:245))
        (PORT datab[15] (299:299:299) (246:246:246))
        (PORT datab[16] (410:410:410) (352:352:352))
        (PORT datab[17] (295:295:295) (245:245:245))
        (IOPATH dataa dataout (1719:1719:1719) (1719:1719:1719))
        (IOPATH datab dataout (1676:1676:1676) (1676:1676:1676))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_mac_out")
    (INSTANCE Mult0\|auto_generated\|mac_out4)
    (DELAY
      (ABSOLUTE
        (IOPATH dataa dataout (61:61:61) (64:64:64))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~0)
    (DELAY
      (ABSOLUTE
        (PORT dataa (431:431:431) (499:499:499))
        (PORT datab (551:551:551) (629:629:629))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (190:190:190) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~2)
    (DELAY
      (ABSOLUTE
        (PORT dataa (436:436:436) (503:503:503))
        (PORT datab (432:432:432) (499:499:499))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~4)
    (DELAY
      (ABSOLUTE
        (PORT dataa (539:539:539) (617:617:617))
        (PORT datab (403:403:403) (470:470:470))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~6)
    (DELAY
      (ABSOLUTE
        (PORT dataa (459:459:459) (534:534:534))
        (PORT datab (416:416:416) (485:485:485))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~8)
    (DELAY
      (ABSOLUTE
        (PORT dataa (451:451:451) (525:525:525))
        (PORT datab (417:417:417) (479:479:479))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~10)
    (DELAY
      (ABSOLUTE
        (PORT dataa (412:412:412) (480:480:480))
        (PORT datab (557:557:557) (645:645:645))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (166:166:166) (174:174:174))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~12)
    (DELAY
      (ABSOLUTE
        (PORT dataa (430:430:430) (496:496:496))
        (PORT datab (544:544:544) (631:631:631))
        (IOPATH dataa combout (186:186:186) (180:180:180))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datab combout (191:191:191) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE Mult0\|auto_generated\|op_1\~14)
    (DELAY
      (ABSOLUTE
        (PORT datab (408:408:408) (469:469:469))
        (PORT datad (442:442:442) (511:511:511))
        (IOPATH datab combout (196:196:196) (205:205:205))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
)
