/*
 * MSPM0G3507 SPI配置实现文件
 */

#include "ti_msp_dl_config.h"

// SPI时钟配置：1MHz SPI时钟
const DL_SPI_ClockConfig gSPI_0ClockConfig = {
    .clockSel = DL_SPI_CLOCK_BUSCLK,
    .divideRatio = DL_SPI_CLOCK_DIVIDE_RATIO_32  // 32MHz/32 = 1MHz
};

/**
 * 系统初始化函数
 */
void SYSCFG_DL_init(void)
{
    // 配置系统时钟为32MHz
    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    
    // 使能SPI和GPIO时钟
    DL_SYSCTL_enablePower(DL_SYSCTL_POWER_DOMAIN_PERIPHERAL);
    delay_cycles(POWER_STARTUP_DELAY);
    
    // 配置SPI引脚
    DL_GPIO_initPeripheralOutputFunction(GPIO_IOMUX_SPI0_SCLK, DL_GPIO_IOMUX_SPI0_SCLK);
    DL_GPIO_initPeripheralOutputFunction(GPIO_IOMUX_SPI0_PICO, DL_GPIO_IOMUX_SPI0_PICO);
    
    // 配置CS引脚为GPIO输出
    DL_GPIO_initDigitalOutput(GPIO_GRP_0_CS_IOMUX);
    DL_GPIO_setPins(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);  // CS默认高电平
    DL_GPIO_enableOutput(GPIO_GRP_0_PORT, GPIO_GRP_0_CS_PIN);
    
    // 配置SPI为主机模式
    DL_SPI_Config spiConfig = {
        .mode = DL_SPI_MODE_CONTROLLER,           // 主机模式
        .frameFormat = DL_SPI_FRAME_FORMAT_MOTO4_POL0_PHA0,  // Mode 0
        .parity = DL_SPI_PARITY_NONE,
        .dataSize = DL_SPI_DATA_SIZE_16,          // 16位数据
        .bitOrder = DL_SPI_BIT_ORDER_MSB_FIRST,   // MSB先传
        .chipSelectPin = DL_SPI_CHIP_SELECT_NONE  // 手动控制CS
    };
    
    DL_SPI_init(SPI_0_INST, &spiConfig);
    DL_SPI_setClockConfig(SPI_0_INST, &gSPI_0ClockConfig);
    DL_SPI_enable(SPI_0_INST);
}

// 延时函数（用于电源稳定）
static void delay_cycles(uint32_t cycles)
{
    while(cycles--) {
        __NOP();
    }
}

#define POWER_STARTUP_DELAY    (16)
#define GPIO_IOMUX_SPI0_SCLK   (IOMUX_PINCM6)   // PA6
#define GPIO_IOMUX_SPI0_PICO   (IOMUX_PINCM5)   // PA5  
#define GPIO_GRP_0_CS_IOMUX    (IOMUX_PINCM4)   // PA4
