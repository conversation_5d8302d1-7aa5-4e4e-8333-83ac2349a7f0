{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1754014632085 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1754014632086 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 10:17:12 2025 " "Processing started: Fri Aug 01 10:17:12 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1754014632086 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1754014632086 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1754014632086 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1754014632204 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Tri " "Found entity 1: ROM_Tri" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632226 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632226 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/sel_wave.v" { { "Info" "ISGN_ENTITY_NAME" "1 sel_wave " "Found entity 1: sel_wave" {  } { { "../rtl/sel_wave.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/sel_wave.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632227 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632227 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_delay.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_delay " "Found entity 1: key_delay" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632228 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632228 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/key_con.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_con " "Found entity 1: key_con" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/key_con.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632229 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632229 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/add_32bit.v" { { "Info" "ISGN_ENTITY_NAME" "1 add_32bit " "Found entity 1: add_32bit" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632230 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632230 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Sin " "Found entity 1: ROM_Sin" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632231 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632231 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/rtl/dac904_top.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_TOP " "Found entity 1: DAC904_TOP" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632232 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632232 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22 (2)/fpga_cyclone_iv_e22/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK " "Found entity 1: PLL_CLK" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632234 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632234 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "DAC904_TOP " "Elaborating entity \"DAC904_TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Quartus II" 0 -1 1754014632277 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK PLL_CLK:u_PLL_CLK " "Elaborating entity \"PLL_CLK\" for hierarchy \"PLL_CLK:u_PLL_CLK\"" {  } { { "../rtl/DAC904_TOP.v" "u_PLL_CLK" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 92 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632279 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "altpll_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632295 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborated megafunction instantiation \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Instantiated megafunction \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 5 " "Parameter \"clk0_divide_by\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 2 " "Parameter \"clk0_multiply_by\" = \"2\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=PLL_CLK " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=PLL_CLK\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_USED " "Parameter \"port_areset\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_UNUSED " "Parameter \"port_clk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632298 ""}  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1754014632298 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/pll_clk_altpll.v 1 1 " "Found 1 design units, including 1 entities, in source file db/pll_clk_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK_altpll " "Found entity 1: PLL_CLK_altpll" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632333 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632333 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK_altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated " "Elaborating entity \"PLL_CLK_altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632334 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Sin ROM_Sin:u_ROM_Sin " "Elaborating entity \"ROM_Sin\" for hierarchy \"ROM_Sin:u_ROM_Sin\"" {  } { { "../rtl/DAC904_TOP.v" "u_ROM_Sin" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 99 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632337 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632355 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Sin_Wave.mif " "Parameter \"init_file\" = \"Sin_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a CLOCK0 " "Parameter \"outdata_reg_a\" = \"CLOCK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632356 ""}  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1754014632356 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_aj91.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_aj91.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_aj91 " "Found entity 1: altsyncram_aj91" {  } { { "db/altsyncram_aj91.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_aj91.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632383 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632383 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_aj91 ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated " "Elaborating entity \"altsyncram_aj91\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632384 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Tri ROM_Tri:ROM_Tri " "Elaborating entity \"ROM_Tri\" for hierarchy \"ROM_Tri:ROM_Tri\"" {  } { { "../rtl/DAC904_TOP.v" "ROM_Tri" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632387 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632390 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Tri_Wave.mif " "Parameter \"init_file\" = \"Tri_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632391 ""}  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1754014632391 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_4aa1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_4aa1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_4aa1 " "Found entity 1: altsyncram_4aa1" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632419 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632419 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_4aa1 ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated " "Elaborating entity \"altsyncram_4aa1\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632420 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "add_32bit add_32bit:u_add_32bit " "Elaborating entity \"add_32bit\" for hierarchy \"add_32bit:u_add_32bit\"" {  } { { "../rtl/DAC904_TOP.v" "u_add_32bit" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 114 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632422 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sel_wave sel_wave:u_sel_wave " "Elaborating entity \"sel_wave\" for hierarchy \"sel_wave:u_sel_wave\"" {  } { { "../rtl/DAC904_TOP.v" "u_sel_wave" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 128 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632422 ""}
{ "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_HDR" "" "Synthesized away the following node(s):" { { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_SUB_HDR" "RAM " "Synthesized away the following RAM node(s):" { { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[0\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[0\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 34 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[1\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[1\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 55 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a1"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[2\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[2\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 76 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[3\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[3\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 97 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a3"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[4\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[4\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 118 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[5\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[5\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 139 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a5"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[6\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[6\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 160 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[7\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[7\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 181 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a7"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[8\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[8\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 202 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[9\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[9\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 223 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a9"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[10\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[10\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 244 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[11\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[11\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 265 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a11"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[12\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[12\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 286 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12"} { "Warning" "WCDB_SGATE_CDB_SGATE_SWEPT_NODE" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[13\] " "Synthesized away node \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\|q_a\[13\]\"" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 307 2 0 } } { "altsyncram.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } } { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 106 0 0 } }  } 0 14320 "Synthesized away node \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632459 "|DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a13"}  } {  } 0 14285 "Synthesized away the following %1!s! node(s):" 0 0 "Quartus II" 0 -1 1754014632459 ""}  } {  } 0 14284 "Synthesized away the following node(s):" 0 0 "Quartus II" 0 -1 1754014632459 ""}
{ "Info" "ILPMS_INFERENCING_SUMMARY" "1 " "Inferred 1 megafunctions from design logic" { { "Info" "ILPMS_LPM_MULT_INFERRED" "Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"Mult0\"" {  } { { "../rtl/DAC904_TOP.v" "Mult0" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 141 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632578 ""}  } {  } 0 278001 "Inferred %1!llu! megafunctions from design logic" 0 0 "Quartus II" 0 -1 1754014632578 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "lpm_mult:Mult0 " "Elaborated megafunction instantiation \"lpm_mult:Mult0\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 141 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "lpm_mult:Mult0 " "Instantiated megafunction \"lpm_mult:Mult0\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHA 26 " "Parameter \"LPM_WIDTHA\" = \"26\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHB 12 " "Parameter \"LPM_WIDTHB\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHP 38 " "Parameter \"LPM_WIDTHP\" = \"38\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHR 38 " "Parameter \"LPM_WIDTHR\" = \"38\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHS 1 " "Parameter \"LPM_WIDTHS\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_REPRESENTATION UNSIGNED " "Parameter \"LPM_REPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_A_IS_CONSTANT NO " "Parameter \"INPUT_A_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_B_IS_CONSTANT NO " "Parameter \"INPUT_B_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "MAXIMIZE_SPEED 5 " "Parameter \"MAXIMIZE_SPEED\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1754014632598 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 141 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1754014632598 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mult_9dt.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mult_9dt.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mult_9dt " "Found entity 1: mult_9dt" {  } { { "db/mult_9dt.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/par/db/mult_9dt.tdf" 30 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1754014632625 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1754014632625 ""}
{ "Info" "IMLS_MLS_IGNORED_SUMMARY" "24 " "Ignored 24 buffer(s)" { { "Info" "IMLS_MLS_IGNORED_SOFT" "24 " "Ignored 24 SOFT buffer(s)" {  } {  } 0 13019 "Ignored %1!d! SOFT buffer(s)" 0 0 "Quartus II" 0 -1 1754014632775 ""}  } {  } 0 13014 "Ignored %1!d! buffer(s)" 0 0 "Quartus II" 0 -1 1754014632775 ""}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 63 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Quartus II" 0 -1 1754014632776 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Quartus II" 0 -1 1754014632776 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "PD GND " "Pin \"PD\" is stuck at GND" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 9 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Quartus II" 0 -1 1754014632785 "|DAC904_TOP|PD"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Quartus II" 0 -1 1754014632785 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Quartus II" 0 -1 1754014632862 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Quartus II" 0 -1 1754014633003 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014633003 ""}
{ "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN_HDR" "3 " "Design contains 3 input pin(s) that do not drive logic" { { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[0\] " "No output dependent on input pin \"KEY_IN\[0\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014633028 "|DAC904_TOP|KEY_IN[0]"} { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[1\] " "No output dependent on input pin \"KEY_IN\[1\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014633028 "|DAC904_TOP|KEY_IN[1]"} { "Warning" "WCUT_CUT_UNNECESSARY_INPUT_PIN" "KEY_IN\[2\] " "No output dependent on input pin \"KEY_IN\[2\]\"" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22 (2)/FPGA_Cyclone_IV_E22/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 4 0 0 } }  } 0 15610 "No output dependent on input pin \"%1!s!\"" 0 0 "Quartus II" 0 -1 1754014633028 "|DAC904_TOP|KEY_IN[2]"}  } {  } 0 21074 "Design contains %1!d! input pin(s) that do not drive logic" 0 0 "Quartus II" 0 -1 1754014633028 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "136 " "Implemented 136 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Quartus II" 0 -1 1754014633028 ""} { "Info" "ICUT_CUT_TM_OPINS" "16 " "Implemented 16 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Quartus II" 0 -1 1754014633028 ""} { "Info" "ICUT_CUT_TM_LCELLS" "93 " "Implemented 93 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Quartus II" 0 -1 1754014633028 ""} { "Info" "ICUT_CUT_TM_RAMS" "14 " "Implemented 14 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Quartus II" 0 -1 1754014633028 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Quartus II" 0 -1 1754014633028 ""} { "Info" "ICUT_CUT_TM_DSP_ELEM" "4 " "Implemented 4 DSP elements" {  } {  } 0 21062 "Implemented %1!d! DSP elements" 0 0 "Quartus II" 0 -1 1754014633028 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Quartus II" 0 -1 1754014633028 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 23 s Quartus II 64-Bit " "Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 23 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4646 " "Peak virtual memory: 4646 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1754014633039 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 10:17:13 2025 " "Processing ended: Fri Aug 01 10:17:13 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1754014633039 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1754014633039 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1754014633039 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1754014633039 ""}
