set_global_assignment -name IP_TOOL_NAME "ALTPLL"
set_global_assignment -name IP_TOOL_VERSION "13.0"
set_global_assignment -name VERILOG_FILE [file join $::quartus(qip_path) "PLL_CLK.v"]
set_global_assignment -name MISC_FILE [file join $::quartus(qip_path) "PLL_CLK.bsf"]
set_global_assignment -name MISC_FILE [file join $::quartus(qip_path) "PLL_CLK_bb.v"]
set_global_assignment -name MISC_FILE [file join $::quartus(qip_path) "PLL_CLK.ppf"]
